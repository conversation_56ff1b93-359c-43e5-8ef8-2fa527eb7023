package com.immomo.recommend.db.es.vector.configure;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 服务端信息配置注入对象 配置内容见application.yml
 */
@Component
@ConfigurationProperties(prefix = "recommend.provider")
public class ProviderProperties {

    private List<ProviderProperty> servicesMeta = new ArrayList<>();

    public List<ProviderProperty> getServicesMeta() {
        return servicesMeta;
    }

    public void setServicesMeta(List<ProviderProperty> servicesMeta) {
        this.servicesMeta = servicesMeta;
    }

    public static class ProviderProperty{
        private String serviceUri;
        private String interfaces;
        private int timeout;
        private int processTimeout;

        public String getServiceUri() {
            return serviceUri;
        }

        public void setServiceUri(String serviceUri) {
            this.serviceUri = serviceUri;
        }
        public String getInterfaces() {
            return interfaces;
        }

        public void setInterfaces(String interfaces) {
            this.interfaces = interfaces;
        }

        public int getTimeout() {
            return timeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }

        public int getProcessTimeout() {
            return processTimeout;
        }

        public void setProcessTimeout(int processTimeout) {
            this.processTimeout = processTimeout;
        }
    }
}
