package com.immomo.recommend.db.es.vector.configure;

import com.immomo.configcenter2.client.ConfigCenter;
import com.immomo.configcenter2.client.GeneralConfig;
import com.immomo.configcenter2.client.valuetype.StringValuetype;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/03/03 11:43
 */
public class ConfigManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigManager.class);

    static {
        init();
    }

    private ConfigManager() {
    }

    private static void init() {
        LOGGER.info("====VectorDb ConfigManager init start====");

        try {
            ConfigCenter.init();
        } catch (IOException var1) {
            LOGGER.error("VectorDb ConfigManager init error", var1);
            throw new RuntimeException(var1);
        }
        LOGGER.info("====VectorDb ConfigManager init success====");
    }


    public static String getVectorDbServiceUri() {
        String vectorDbServiceUri = GeneralConfig.getConfigWithDefaultValue("vectorDbServiceUri",
                StringValuetype.INSTANCE, "http://es-vector-db-eco.momo.com:19090");
        return vectorDbServiceUri;
    }

    public static String getApiKey() {
        String apiKey = GeneralConfig.getConfigWithDefaultValue("apiKey",
                StringValuetype.INSTANCE, "clFrMldwVUI5ZFpIeVh1eDhMSGk6MTFfMFJ2WkRUQjJMTmY4b25yQVNXQQ==");
        return apiKey;
    }
}
