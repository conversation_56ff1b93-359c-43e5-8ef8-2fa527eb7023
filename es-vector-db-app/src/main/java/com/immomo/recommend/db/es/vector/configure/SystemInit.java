package com.immomo.recommend.db.es.vector.configure;

import com.immomo.moa.util.InetAddressUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * Created by momo on 2018/9/27.
 */
@Component
public class SystemInit implements InitializingBean {
    private String ip = "";
    private String host = "";
    private String machine = "";
    private String hostIp = "";

    public String getIp() {
        return ip;
    }

    public String getHost() {
        return host;
    }

    public String getMachine() {
        return machine;
    }

    public String getHostIp() {
        return hostIp;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ip = InetAddressUtils.getLocalAddress();
        host = System.getenv("HOSTNAME");
        machine = host.substring(host.lastIndexOf("-") + 1);
        hostIp = host + "(" + ip + ")";
    }
}
