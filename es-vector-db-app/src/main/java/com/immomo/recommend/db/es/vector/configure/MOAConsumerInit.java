package com.immomo.recommend.db.es.vector.configure;

import com.immomo.mcf.util.MapUtils;
import com.immomo.mcf.util.PropertiesManager;
import com.immomo.mcf.util.StringUtils;
import com.immomo.moa.api.Constants;
import com.immomo.moa.exception.InitializationException;
import com.immomo.moa.service.consumer.PropertyBasedServiceConsumerFactory;
import com.immomo.moa.service.consumer.ServiceConsumerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * MOA 客户端初始化类 在spring初始化bean的时候解析 moa_client.properties文件
 * 将配置的客户端注入到spring当中 可以在项目任务中使用注解注入客户端代理类
 */
@Configuration
public class MOAConsumerInit implements BeanDefinitionRegistryPostProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(MOAConsumerInit.class);

    @Override
    @SuppressWarnings("NullableProblems")
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry beanDefinitionRegistry) {
        LOGGER.info("postProcessBeanDefinitionRegistry");
    }

    @Override
    @SuppressWarnings("NullableProblems")
    public void postProcessBeanFactory(ConfigurableListableBeanFactory configurableListableBeanFactory) {
        try {
            ServiceConsumerFactory scf = new PropertyBasedServiceConsumerFactory("moa_client.properties");
            PropertiesManager propertiesManager = new PropertiesManager("moa_client.properties");
            Map<String, Object> serviceConfigs = propertiesManager.getPropsByPrefix(Constants.KEY_PREFIX_ROOT);
            serviceConfigs.keySet().forEach(serviceUri -> {
                Map serviceConfig = (Map) serviceConfigs.get(serviceUri);
                String interfaceName = MapUtils.getString(serviceConfig, Constants.KEY_INTERFACE);
                LOGGER.info(StringUtils.format("初始化客户端 [serviceUri = {0},interface = {1}]", serviceUri, interfaceName));
                configurableListableBeanFactory.registerSingleton(interfaceName.substring(interfaceName.lastIndexOf('.')), scf.getInstance(serviceUri));
                LOGGER.info(StringUtils.format("初始化成功 [serviceUri = {0},interface = {1}]", serviceUri, interfaceName));
            });
        } catch (InitializationException e) {
            LOGGER.error("MOA Consumer init error", e);
        }
    }
}
