package com.immomo.recommend.db.es.vector.configure;

import com.immomo.recommend.db.es.vector.impl.VectorApiImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/11/16 15:11
 */
@Configuration
public class BeanInit {

    @Bean
    public VectorApiImpl vectorApi() {
        String serviceUri = ConfigManager.getVectorDbServiceUri();
        String apiKey = ConfigManager.getApiKey();
        return new VectorApiImpl(serviceUri, apiKey);
    }
}
