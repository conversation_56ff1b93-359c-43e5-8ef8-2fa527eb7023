package com.immomo.recommend.db.es.vector.configure;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统属性配置注入对象 在application.yml中配置
 * MOA 服务端启动的时候会使用到系统变量
 */
@Component
@ConfigurationProperties(prefix = "recommend.system")
public class SystemProperties {

    private Map<String,String> properties = new HashMap<>();

    public Map<String, String> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, String> properties) {
        this.properties = properties;
    }
}
