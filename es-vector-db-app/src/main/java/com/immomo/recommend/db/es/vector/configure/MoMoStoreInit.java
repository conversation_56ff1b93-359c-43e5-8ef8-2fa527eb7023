package com.immomo.recommend.db.es.vector.configure;

import org.springframework.context.annotation.Configuration;

/**
 * momoStore 配置初始化 添加momoStore只需在添加一个方法即可
 * 然后使用Autowired 加 Qualifier注解 由于会有多个momoStore配置 bean的name默认为方法的名称 也可以指定bean的name
 */
@Configuration
public class MoMoStoreInit {
    //    @Bean
    //    public IStoreDao recallStoreDao() {
    //        JedisPoolConfig jedisConfig = POOL_CONFIGS.get(MEDIUM);
    //        jedisConfig.setJmxEnabled(false);
    //        return StoreDaoFactory.createStoreDao("momostore_rec_data_manage_meta", jedisConfig);
    //    }

    //    @Bean(name = "bloomFilterDao")
    //    public IStoreDao bloomFilterDao() {
    //        return StoreDaoFactory.createStoreDao("momostore_shortvideo_bloom_f");
    //    }
}
