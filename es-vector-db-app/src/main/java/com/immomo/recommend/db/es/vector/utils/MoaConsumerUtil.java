package com.immomo.recommend.db.es.vector.utils;

import com.immomo.mcf.util.MapUtils;
import com.immomo.mcf.util.StringUtils;
import com.immomo.moa.api.Constants;
import com.immomo.moa.exception.MOAException;
import com.immomo.moa.service.ServiceMetadata;
import com.immomo.moa.service.consumer.ServiceConsumerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/09/18 17:41
 */
public class MoaConsumerUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(MoaConsumerUtil.class);

    public static  <T> T getInstance(ServiceConsumerFactory serviceConsumerFactory, String serviceUri, String interfaceName, Map serviceConfig) throws MOAException {
        try {
            ServiceMetadata serviceMetadata = new ServiceMetadata(serviceUri, interfaceName);
            // 服务通讯的协议。
            String protocol = MapUtils.getString(serviceConfig, Constants.KEY_PROTOCOL, Constants.PROTOCOL_TCP);
            serviceMetadata.setImporterProtocol(protocol);
            // 超时时间设置。
            int timeout = MapUtils.getIntValue(serviceConfig, Constants.KEY_TIMEOUT, 0);
            if (timeout > 0) {
                serviceMetadata.addProperty(Constants.KEY_TIMEOUT, timeout);
            }
            String targetUris = MapUtils.getString(serviceConfig, Constants.KEY_TARGET_URIS, "");
            if (targetUris != null && targetUris.length() > 0) {
                serviceMetadata.addProperty(Constants.KEY_TARGET_URIS, targetUris.split(","));
            }
            // 定向路由参数
            String router = MapUtils.getString(serviceConfig, Constants.KEY_ROUTER, "");
            if (StringUtils.isNotEmpty(router)) {
                serviceMetadata.addProperty(Constants.KEY_ROUTER, router);
            }
            String hashAlgorithm = MapUtils.getString(serviceConfig, Constants.KEY_HASH_ALGORITHM, "");
            if (StringUtils.isNotEmpty(hashAlgorithm)) {
                serviceMetadata.addProperty(Constants.KEY_HASH_ALGORITHM, hashAlgorithm);
            }
            serviceConsumerFactory.registerService(serviceMetadata);
            return serviceConsumerFactory.getInstance(serviceUri);
        } catch (Exception e) {
            LOGGER.error("getInstance error", e);
        }
        return null;
    }
}
