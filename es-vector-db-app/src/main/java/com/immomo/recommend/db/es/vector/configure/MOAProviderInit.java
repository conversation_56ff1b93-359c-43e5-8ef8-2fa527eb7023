package com.immomo.recommend.db.es.vector.configure;

import com.immomo.mcf.util.JsonUtils;
import com.immomo.moa.spring.MOAProviderBean;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * MOA 服务初始化类
 * 在Spring 初始化完成之后事件通知
 */
@Configuration
@EnableConfigurationProperties({SystemProperties.class, ProviderProperties.class})
public class MOAProviderInit implements ApplicationListener<ContextRefreshedEvent> {
    private static final Logger LOGGER = LoggerFactory.getLogger(MOAProviderInit.class);
    private final ProviderProperties providerProperties;
    private final SystemProperties systemProperties;
    private final AtomicBoolean init = new AtomicBoolean(false);
    @Resource
    private SystemInit systemInit;
    private static final String SERVICE_URI = "SERVICE_URI";
    private static final String TIME_OUT = "TIME_OUT";
    private static final String PROCESS_TIME_OUT = "PROCESS_TIME_OUT";


    @Autowired
    public MOAProviderInit(ProviderProperties providerProperties, SystemProperties systemProperties) {
        this.providerProperties = providerProperties;
        this.systemProperties = systemProperties;
    }

    @Override
    @SuppressWarnings("NullableProblems")
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        LOGGER.info("host信息:" + systemInit.getHostIp());
        initSystemProperties();
        initServiceProvider(contextRefreshedEvent);
    }


    private void initServiceProvider(ContextRefreshedEvent contextRefreshedEvent) {
        if (!init.get()) {
            List<ProviderProperties.ProviderProperty> serviceMetadataList = providerProperties.getServicesMeta();
            LOGGER.info("serviceMetadataList:{}", JsonUtils.toJSON(serviceMetadataList));
            if (!CollectionUtils.isEmpty(serviceMetadataList)) {
                ProviderProperties.ProviderProperty serviceMetadata = serviceMetadataList.get(0);
                MOAProviderBean moaProviderBean = new MOAProviderBean();
                String configServiceUri = System.getenv(SERVICE_URI);
                String configServiceTimeOut = System.getenv(TIME_OUT);
                String configServiceProcessTimeOut = System.getenv(PROCESS_TIME_OUT);
                if (StringUtils.isNotBlank(configServiceUri)) {
                    LOGGER.info("环境变量SERVICE_URI指定为{}", configServiceUri);
                    serviceMetadata.setServiceUri(configServiceUri);
                }
                try {
                    LOGGER.info("服务[" + serviceMetadata.getServiceUri() + "]初始化开始");
                    Object[] objects = contextRefreshedEvent.getApplicationContext().getBeansOfType(Class.forName(serviceMetadata.getInterfaces())).values().toArray();
                    moaProviderBean.setInstance(objects[0]);
                    moaProviderBean.setServiceUri(serviceMetadata.getServiceUri());
                    moaProviderBean.setInterface(serviceMetadata.getInterfaces());
                    // 自定义服务超时时间
                    if (StringUtils.isNotEmpty(configServiceTimeOut)) {
                        moaProviderBean.setTimeout(Integer.parseInt(configServiceTimeOut));
                    } else {
                        moaProviderBean.setTimeout(serviceMetadata.getTimeout());
                    }
                    if (StringUtils.isNotEmpty(configServiceProcessTimeOut)) {
                        moaProviderBean.setProcessTimeout(Integer.parseInt(configServiceProcessTimeOut));
                    } else {
                        moaProviderBean.setProcessTimeout(serviceMetadata.getProcessTimeout());
                    }
                    moaProviderBean.init();
                    LOGGER.info("服务[" + serviceMetadata.getServiceUri() + "]初始化成功");
                    init.compareAndSet(false, true);
                    LOGGER.info("ForKJoinPool Thread " + ForkJoinPool.commonPool().getParallelism());
                } catch (Exception e) {
                    LOGGER.error("服务初始化失败 ", e);
                }
            } else {
                LOGGER.info("无服务信息配置");
                init.compareAndSet(false, true);
            }
        }
    }

    private void initSystemProperties() {
        systemProperties.getProperties().forEach(System::setProperty);
        systemProperties.getProperties().forEach((k, v) -> {
            LOGGER.info("System Property,key:{},value:{}", k, v);
        });
    }
}
