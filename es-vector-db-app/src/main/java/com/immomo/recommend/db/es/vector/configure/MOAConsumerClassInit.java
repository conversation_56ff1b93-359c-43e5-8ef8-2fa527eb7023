package com.immomo.recommend.db.es.vector.configure;

import com.immomo.moa.api.Constants;
import com.immomo.moa.api.RunMode;
import com.immomo.moa.exception.MOAException;
import com.immomo.moa.service.consumer.ServiceConsumerFactory;
import com.immomo.recommend.db.es.vector.utils.MoaConsumerUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

/**
 * MOA 客户端初始化类 在spring初始化bean的时候解析 moa_client.properties文件
 * 将配置的客户端注入到spring当中 可以在项目任务中使用注解注入客户端代理类
 */
@Configuration
@EnableConfigurationProperties({SystemProperties.class})
public class MOAConsumerClassInit {
    private static final Logger LOGGER = LoggerFactory.getLogger(MOAConsumerClassInit.class);
    private static final ServiceConsumerFactory serviceConsumerFactory = new ServiceConsumerFactory();
    @Value("${recommend.consumer.runMode}")
    private String runMode;
    @Resource
    private SystemProperties systemProperties;

    @PostConstruct
    public void init() {
        serviceConsumerFactory.setRunMode(runMode());
    }

    public static ServiceConsumerFactory getServiceConsumerFactory() {
        return serviceConsumerFactory;
    }


    private RunMode runMode() {
        System.setProperty(Constants.KEY_RUN_MODE, runMode);
        return RunMode.valueOf(runMode);
    }

    private <T> T getInstance(String serviceUri, String interfaceName, Map serviceConfig) throws MOAException {
        return MoaConsumerUtil.getInstance(serviceConsumerFactory, serviceUri, interfaceName, serviceConfig);
    }
}
