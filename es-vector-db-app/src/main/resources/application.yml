recommend:
  provider:
    servicesMeta:
      - serviceUri: "/service/recommend-vector-db"
        interfaces: "com.immomo.recommend.db.es.vector.VectorApi"
        timeout: 5000
        processTimeout: 5000
  system:
    properties:
      moaLogPath: "/home/<USER>/moa-service/recommend-vector-db/"
      moaPort: "16781"
      runMode: "ONLINE"
      protocol: "REDIS"
  consumer:
    runMode: "ONLINE"

vetor:
  dbConfig:
    userName: "root"
    password: "123456"
    host: "127.0.0.1"
    port: "9200"

