<?xml version="1.0" encoding="UTF-8"?>
<configuration status="INFO" monitorInterval="10">
    <Properties>
        <Property name="logPath">/home/<USER>/moa-service/recommend-vector-db/</Property>
    </Properties>
    <appenders>
        <!--这个输出控制台的配置-->
        <console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} %5p (%F#%M:%L) %t - %m%n"/>
        </console>
        <RollingRandomAccessFile name="recommend-info" fileName="${logPath}/recommend.log"
                                 filePattern="${logPath}/recommend-%d{yyyy-MM-dd-HH}.log.gz" immediateFlush="false"
                                 append="false">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p (%F:%L) %t:%m%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="4"/>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="recommend-warn" fileName="${logPath}/recommend-warn.log"
                                 filePattern="${logPath}/recommend-warn-%d{yyyy-MM-dd}.log" immediateFlush="false"
                                 append="false">
            <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} %5p (%F#%M:%L) %t - %m%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="100MB"/>
                <!--<SizeBasedTriggeringPolicy size="100 MB"/>-->
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件，这里设置了20 -->
            <DefaultRolloverStrategy max="5"/>

        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="recommend-error" fileName="${logPath}/rec-error.log"
                                 filePattern="${logPath}/rec-error-%d{yyyy-MM-dd}.log" immediateFlush="false"
                                 append="false">
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} %5p (%F#%M:%L) %t - %m%n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件，这里设置了20 -->
            <DefaultRolloverStrategy max="10"/>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="app" fileName="${logPath}/app.log"
                                 filePattern="${logPath}/app-%d{yyyy-MM-dd}.log" immediateFlush="false" append="false">
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} %5p (%F#%M:%L) %t - %m%n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件，这里设置了20 -->
            <DefaultRolloverStrategy max="10"/>
        </RollingRandomAccessFile>
    </appenders>

    <!--然后定义logger，只有定义了logger并引入的appender，appender才会生效-->
    <loggers>
        <AsyncLogger name="com.immomo.recommend.brpc" level="INFO" additivity="false" includeLocation="true">
            <AppenderRef ref="app"/>
        </AsyncLogger>
        <!--依赖jar包日志-->
        <AsyncLogger name="com.immomo.recommend" level="INFO" additivity="false" includeLocation="true">
            <AppenderRef ref="recommend-info"/>
            <AppenderRef ref="recommend-warn"/>
            <AppenderRef ref="recommend-error"/>
        </AsyncLogger>
        <!--推荐框架-->
        <AsyncLogger name="com.immomo.recommendation" level="INFO" additivity="false" includeLocation="true">
            <AppenderRef ref="recommend-info"/>
        </AsyncLogger>
        <!--启动日志-->
        <AsyncLogger name="com.immomo.recommend.data.manage" level="INFO" additivity="false"
                     includeLocation="true">
            <AppenderRef ref="recommend-info"/>
            <AppenderRef ref="recommend-warn"/>
            <AppenderRef ref="recommend-error"/>
        </AsyncLogger>
        <AsyncLogger name="com.immomo.moa" level="INFO" additivity="false" includeLocation="true">
            <AppenderRef ref="recommend-info"/>
            <AppenderRef ref="recommend-warn"/>
            <AppenderRef ref="recommend-error"/>
        </AsyncLogger>
        <AsyncLogger name="com.immomo.mcf" level="INFO" additivity="false" includeLocation="true">
            <AppenderRef ref="recommend-info"/>
            <AppenderRef ref="recommend-warn"/>
            <AppenderRef ref="recommend-error"/>
        </AsyncLogger>
        <AsyncLogger name="com.immomo.monitor" level="INFO" additivity="false" includeLocation="true">
            <AppenderRef ref="app"/>
        </AsyncLogger>
        <AsyncLogger name="com.immomo.moa.client" level="INFO" additivity="false" includeLocation="true">
            <AppenderRef ref="app"/>
        </AsyncLogger>
        <AsyncLogger name="com.immomo.moa.mk" level="INFO" additivity="false" includeLocation="true">
            <AppenderRef ref="app"/>
        </AsyncLogger>
        <AsyncLogger name="com.immomo" level="INFO" additivity="false" includeLocation="true">
            <AppenderRef ref="app"/>
        </AsyncLogger>
        <AsyncRoot level="INFO">
            <AppenderRef ref="app"/>
        </AsyncRoot>
    </loggers>
</configuration>
