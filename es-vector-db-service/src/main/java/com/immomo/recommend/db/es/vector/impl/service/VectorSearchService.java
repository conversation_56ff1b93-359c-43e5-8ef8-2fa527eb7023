package com.immomo.recommend.db.es.vector.impl.service;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.KnnSearch;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.ScriptScoreQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.WrapperQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.immomo.recommend.db.es.vector.bean.VectorWithProfile;
import com.immomo.recommend.db.es.vector.impl.Constants;
import com.immomo.recommend.db.es.vector.impl.utils.QueryUtils;
import com.immomo.recommend.db.es.vector.impl.utils.VectorUtils;
import com.immomo.recommend.db.es.vector.query.builder.QueryBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.RestHighLevelClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/02/24 17:14
 */
public class VectorSearchService {
    private static final Logger LOGGER = LoggerFactory.getLogger(VectorSearchService.class);
    private final ElasticsearchClient esClient;
    private final RestHighLevelClient restHighLevelClient;

    public VectorSearchService(String serverUrl, String apiKey) {
        BasicHeader authorization = new BasicHeader("Authorization", "ApiKey " + apiKey);
        HttpHost httpHost = HttpHost.create(serverUrl);
        Header[] defaultHeaders = {authorization};
        RestClient restClient = RestClient.builder(httpHost).setDefaultHeaders(defaultHeaders).build();
        restHighLevelClient = new RestHighLevelClientBuilder(restClient).setApiCompatibilityMode(true).build();
        ElasticsearchTransport transport = new RestClientTransport(restClient, new JacksonJsonpMapper());
        esClient = new ElasticsearchClient(transport);
    }

    public List<Hit<Map>> searchBoolIndex(String indexName, float[] searchVector, int limit, Float scoreGt, Float scoreLt, String queryDslJson) {
        Query filterQuery;
        try {
            if (StringUtils.isEmpty(queryDslJson)) {
                // 如果没有过滤条件，则匹配所有文档
                filterQuery = QueryBuilders.matchAll(m -> m);
            } else {
                // 如果有过滤条件，则使用WrapperQuery来包装用户的JSON查询
                String base64Query = java.util.Base64.getEncoder().encodeToString(queryDslJson.getBytes());
                filterQuery = QueryBuilders.wrapper(w -> w.query(base64Query));
            }
        } catch (Exception e) {
            LOGGER.error("queryDslJson格式错误", e);
            return Collections.emptyList();
        }

        // 将查询向量从 float[] 转换为 List<String> 以便传递给脚本
        List<String> queryVectorStr = new ArrayList<>(searchVector.length);
        for (float v : searchVector) {
            queryVectorStr.add(String.valueOf(v));
        }

        // 定义Painless脚本
        String scriptSource =
                "if (!doc.containsKey(params.field) || doc[params.field].empty) { return 0.0; }" +
                "def doc_vector = doc[params.field].value.splitOnToken(' ');" +
                "def query_vector = params.query_vector;" +
                "if (query_vector.length != doc_vector.length) { return 0.0; }" +
                "double score = 0;" +
                "for (int i = 0; i < query_vector.length; i++) {" +
                "  if (query_vector[i].equals(doc_vector[i])) {" +
                "    score++;" +
                "  }" +
                "}" +
                "return score;";

        // 构建ScriptScoreQuery
        ScriptScoreQuery scriptScoreQuery = ScriptScoreQuery.of(ssq -> ssq
                .query(filterQuery)
                .script(s -> s
                        .inline(is -> is
                                .source(scriptSource)
                                .params("field", JsonData.of(Constants.BOOL_VECTOR_FIELD_NAME))
                                .params("query_vector", JsonData.of(queryVectorStr))
                        )
                )
        );

        // 构建最终的SearchRequest
        SearchRequest.Builder requestBuilder = new SearchRequest.Builder()
                .index(indexName)
                .query(scriptScoreQuery._toQuery())
                .size(limit);

        if (scoreGt != null) {
            requestBuilder.minScore(scoreGt.doubleValue());
        }

        try {
            SearchResponse<Map> search = esClient.search(requestBuilder.build(), Map.class);
            return search.hits().hits();
        } catch (IOException e) {
            LOGGER.error("search bool index with script_score error", e);
        }
        return Collections.emptyList();
    }

    public List<Hit<Map>> search(String indexName, float[] searchVector, int limit, Float scoreGt, Float scoreLt, String queryDslJson) {
        try {
            if (StringUtils.isNotEmpty(queryDslJson)) {
                //validate json
                QueryUtils.fromJson(queryDslJson);
            }
        } catch (Exception e) {
            LOGGER.error("queryDslJson格式错误", e);
            return Collections.emptyList();
        }
        List<Float> queryEmbedding = new ArrayList<>(searchVector.length);
        for (float v : searchVector) {
            queryEmbedding.add(v);
        }
        KnnSearch knnSearch = buildKnnSearch(limit, queryDslJson, queryEmbedding);
        SearchRequest searchRequest = buildSearchRequest(indexName, scoreGt, knnSearch, limit);
        try {
            SearchResponse<Map> search = esClient.search(searchRequest, Map.class);
            List<Hit<Map>> hits = search.hits().hits();
            return hits;
        } catch (IOException e) {
            LOGGER.error("search similar vector error", e);
        }
        return Collections.emptyList();
    }

    private static SearchRequest buildSearchRequest(String indexName, Float scoreGt, KnnSearch knnSearch, int limit) {
        SearchRequest searchRequest;
        if (scoreGt != null) {
            searchRequest = SearchRequest.of(s -> s.index(indexName).minScore(scoreGt.doubleValue()).knn(knnSearch).size(limit));
        } else {
            searchRequest = SearchRequest.of(s -> s.index(indexName).knn(knnSearch).size(limit));
        }
        return searchRequest;
    }

    private static KnnSearch buildKnnSearch(int limit, String queryDslJson, List<Float> queryEmbedding) {
        KnnSearch knnSearch;
        if (StringUtils.isEmpty(queryDslJson)) {
            knnSearch = new KnnSearch.Builder().field(Constants.VECTOR_FIELD_NAME)
                    .queryVector(queryEmbedding)
                    .k(limit)
                    .build();
        } else {
            String base64Query = Base64.getEncoder().encodeToString(queryDslJson.getBytes());
            WrapperQuery.Builder wrapperQuery = QueryBuilders.wrapper().query(base64Query);
            knnSearch = new KnnSearch.Builder().field(Constants.VECTOR_FIELD_NAME)
                    .queryVector(queryEmbedding)
                    .k(limit)
                    .filter(wrapperQuery.build()._toQuery())
                    .build();
        }
        return knnSearch;
    }


    public VectorWithProfile search(String indexName, String id) {
        GetRequest request = new GetRequest(indexName, id);
        try {
            GetResponse getResponse = restHighLevelClient.get(request, RequestOptions.DEFAULT);
            Map<String, Object> sourceAsMap = getResponse.getSourceAsMap();
            if (sourceAsMap == null) {
                return null;
            }
            VectorWithProfile result = extractVectorWithProfile(sourceAsMap);
            if (result != null) {
                return result;
            }
        } catch (IOException e) {
            LOGGER.error("search by indexName:{},id:{} error", indexName, id, e);
        }
        return null;
    }

    private VectorWithProfile extractVectorWithProfile(Map<String, Object> sourceAsMap) {
        Object vectorObject = sourceAsMap.get(Constants.VECTOR_FIELD_NAME);
        if (vectorObject instanceof ArrayList<?>) {
            return createVectorWithProfileFromList(sourceAsMap, (ArrayList<?>) vectorObject, Constants.VECTOR_FIELD_NAME);
        }
        vectorObject = sourceAsMap.get(Constants.BOOL_VECTOR_FIELD_NAME);
        if (vectorObject instanceof String) {
            return createVectorWithProfileFromString(sourceAsMap, (String) vectorObject, Constants.BOOL_VECTOR_FIELD_NAME);
        }
        return null;
    }

    private VectorWithProfile createVectorWithProfileFromList(Map<String, Object> sourceAsMap, ArrayList<?> vectorData, String fieldName) {
        List<Double> doubleList = (List<Double>) vectorData;
        float[] vector = new float[doubleList.size()];
        for (int i = 0; i < doubleList.size(); i++) {
            vector[i] = doubleList.get(i).floatValue();
        }
        VectorWithProfile result = new VectorWithProfile();
        result.setVectorData(vector);
        sourceAsMap.remove(fieldName);
        result.setData(sourceAsMap);
        return result;
    }

    private VectorWithProfile createVectorWithProfileFromString(Map<String, Object> sourceAsMap, String vectorData, String fieldName) {
        float[] vector = VectorUtils.toVector(vectorData);
        VectorWithProfile result = new VectorWithProfile();
        result.setVectorData(vector);
        sourceAsMap.remove(fieldName);
        result.setData(sourceAsMap);
        return result;
    }

    public ElasticsearchClient getEsClient() {
        return esClient;
    }

    public RestHighLevelClient getRestHighLevelClient() {
        return restHighLevelClient;
    }
}
