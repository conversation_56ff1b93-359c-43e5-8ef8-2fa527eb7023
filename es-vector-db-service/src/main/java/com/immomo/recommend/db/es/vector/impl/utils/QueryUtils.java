package com.immomo.recommend.db.es.vector.impl.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.immomo.recommend.db.es.vector.query.builder.DistanceQuery;
import com.immomo.recommend.db.es.vector.query.builder.QueryBuilder;
import com.immomo.recommend.db.es.vector.query.builder.RangeQuery;
import com.immomo.recommend.db.es.vector.query.builder.TermQuery;
import com.immomo.recommend.db.es.vector.query.builder.TermsQuery;

import java.util.Iterator;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/02/27 14:23
 */
public class QueryUtils {
    private static final String regex = "([+-]?\\d*\\.?\\d+)([a-zA-Z]+)";
    private static final Pattern distanceStrPattern = Pattern.compile(regex);


    public static QueryBuilder fromJson(String json) throws Exception {
        QueryBuilder queryBuilder = new QueryBuilder();
        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode jsonNode = objectMapper.readValue(json, ObjectNode.class);
        ObjectNode boolNode = (ObjectNode) jsonNode.get("bool");
        JsonNode filterArray = boolNode.get("filter");
        if (filterArray.isArray()) {
            for (JsonNode filterElement : filterArray) {
                if (filterElement.isObject()) {
                    ObjectNode filterObject = (ObjectNode) filterElement;
                    if (filterObject.has("term")) {
                        ObjectNode termNode = (ObjectNode) filterObject.get("term");
                        termNode.fields().forEachRemaining(field -> {
                            String fieldName = field.getKey();
                            String fieldValue = field.getValue().asText();
                            queryBuilder.add(new TermQuery(fieldName, fieldValue));
                        });
                    } else if (filterObject.has("range")) {
                        ObjectNode rangeNode = (ObjectNode) filterObject.get("range");
                        rangeNode.fields().forEachRemaining(field -> {
                            String fieldName = field.getKey();
                            ObjectNode valueNode = (ObjectNode) field.getValue();
                            String gte = valueNode.has("gte") ? valueNode.get("gte").asText() : null;
                            String lte = valueNode.has("lte") ? valueNode.get("lte").asText() : null;
                            queryBuilder.add(new RangeQuery(fieldName, gte, lte));
                        });
                    } else if (filterObject.has("terms")) {
                        ObjectNode termsNode = (ObjectNode) filterObject.get("terms");
                        termsNode.fields().forEachRemaining(field -> {
                            List<Object> values = null;
                            String fieldName = field.getKey();
                            JsonNode valuesNode = field.getValue();
                            try {
                                values = objectMapper.readValue(valuesNode.toString(), List.class);
                            } catch (JsonProcessingException e) {
                                throw new RuntimeException(e);
                            }
                            queryBuilder.add(new TermsQuery(fieldName, values));
                        });
                    } else if (filterObject.has("geo_distance")) {
                        ObjectNode geoDistanceNode = (ObjectNode) filterObject.get("geo_distance");
                        String distance = geoDistanceNode.has("distance") ? geoDistanceNode.get("distance").asText() : null;
                        Iterator<String> fieldNames = geoDistanceNode.fieldNames();
                        String locationField = "location";
                        while (fieldNames.hasNext()) {
                            String field = fieldNames.next();
                            if (!field.equals("distance")) {
                                locationField = field;
                                break;
                            }
                        }
                        ObjectNode locationNode = geoDistanceNode.has(locationField) ? (ObjectNode) geoDistanceNode.get(locationField) : null;
                        double lat = locationNode != null && locationNode.has("lat") ? locationNode.get("lat").asDouble() : 0.0;
                        double lon = locationNode != null && locationNode.has("lon") ? locationNode.get("lon").asDouble() : 0.0;
                        java.util.regex.Matcher matcher = distanceStrPattern.matcher(distance);
                        if (matcher.matches()) {
                            double distanceValue = Double.parseDouble(matcher.group(1));
                            String unit = matcher.group(2);
                            queryBuilder.add(new DistanceQuery(locationField, distanceValue, unit, lat, lon));
                        }
                    }
                }
            }
        }
        return queryBuilder;
    }
}
