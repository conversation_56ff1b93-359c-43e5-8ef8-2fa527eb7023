package com.immomo.recommend.db.es.vector.impl.validate;

import com.immomo.recommend.db.es.vector.bean.SearchResponse;
import com.immomo.recommend.db.es.vector.config.IndexConfig;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

import static com.immomo.recommend.db.es.vector.type.ResponseStatus.ERROR;

/**
 * <AUTHOR>
 * @date 2025/02/27 19:06
 */
public class Validator {
    public static String validateIndexConfig(IndexConfig config) {
        if (config == null) {
            return "IndexConfig cannot be null";
        }

        if (config.getIndexName() == null || config.getIndexName().isEmpty()) {
            return "Index name cannot be null or empty";
        }

        if (config.getIndexType() == null) {
            return "Index type cannot be null";
        }

        if (config.getMetricType() == null) {
            return "Metric type cannot be null";
        }

        if (config.getShard() <= 0) {
            return "Shard must be greater than 0";
        }

        if (config.getDimension() <= 0) {
            return "Dimension must be greater than 0";
        }

        if (config.getExpireTimeSeconds() < -1) {
            return "Expire time seconds must be -1 or greater";
        }
        return null;
    }


    public static String validateInsertParameters(String indexName, String id, float[] vectorData, Map<String, Object> profile, int expectedDimension) {
        if (indexName == null || indexName.isEmpty()) {
            return "Index name cannot be null or empty";
        }

        if (id == null || id.isEmpty()) {
            return "ID cannot be null or empty";
        }

        if (vectorData == null) {
            return "Vector data cannot be null";
        }

        if (vectorData.length != expectedDimension) {
            return "Vector data length must match the expected dimension";
        }
        return null;
    }

    public static String validateInsertParameters(String indexName, String id, float[] vectorData, int expectedDimension) {
        if (indexName == null || indexName.isEmpty()) {
            return "Index name cannot be null or empty";
        }

        if (id == null || id.isEmpty()) {
            return "ID cannot be null or empty";
        }

        if (vectorData == null) {
            return "Vector data cannot be null";
        }

        if (vectorData.length != expectedDimension) {
            return "Vector data length must match the expected dimension";
        }
        return null;
    }

    public static SearchResponse validateParam(String indexName, float[] searchVector, int limit, Float scoreGt, Float scoreLt, SearchResponse result) {
        if (StringUtils.isEmpty(indexName)) {
            result.setStatus(ERROR.getStatusCode());
            result.setErrorCode("indexName is empty");
            return result;
        }
        if (searchVector == null || searchVector.length == 0) {
            result.setStatus(ERROR.getStatusCode());
            result.setErrorCode("searchVector is empty");
            return result;
        }
        if (limit <= 0) {
            result.setStatus(ERROR.getStatusCode());
            result.setErrorCode("limit <= 0");
            return result;
        }
        if (scoreGt != null && scoreLt != null && scoreGt >= scoreLt) {
            result.setStatus(ERROR.getStatusCode());
            result.setErrorCode("scoreGt >= scoreLt");
            return result;
        }
        return null;
    }
}
