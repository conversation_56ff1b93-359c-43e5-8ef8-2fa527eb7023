package com.immomo.recommend.db.es.vector.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Preconditions;
import com.immomo.mcf.util.JsonUtils;
import com.immomo.recommend.db.es.vector.VectorApi;
import com.immomo.recommend.db.es.vector.bean.BulkResponse;
import com.immomo.recommend.db.es.vector.bean.CreateResponse;
import com.immomo.recommend.db.es.vector.bean.SearchResponse;
import com.immomo.recommend.db.es.vector.bean.Similar;
import com.immomo.recommend.db.es.vector.bean.SimilarItem;
import com.immomo.recommend.db.es.vector.bean.SimilarItemWithData;
import com.immomo.recommend.db.es.vector.bean.Tuple3;
import com.immomo.recommend.db.es.vector.bean.UpdateResponse;
import com.immomo.recommend.db.es.vector.bean.VectorWithProfile;
import com.immomo.recommend.db.es.vector.config.DataField;
import com.immomo.recommend.db.es.vector.config.IndexConfig;
import com.immomo.recommend.db.es.vector.config.IndexSchema;
import com.immomo.recommend.db.es.vector.impl.service.ExpireService;
import com.immomo.recommend.db.es.vector.impl.service.VectorSearchService;
import com.immomo.recommend.db.es.vector.impl.utils.VectorUtils;
import com.immomo.recommend.db.es.vector.impl.validate.Validator;
import com.immomo.recommend.db.es.vector.request.SearchRequest;
import com.immomo.recommend.db.es.vector.type.MetricType;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.DocWriteRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.xcontent.XContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.immomo.recommend.db.es.vector.impl.Constants.isNativeField;
import static com.immomo.recommend.db.es.vector.type.ResponseStatus.ERROR;
import static com.immomo.recommend.db.es.vector.type.ResponseStatus.EXCEPTION;
import static com.immomo.recommend.db.es.vector.type.ResponseStatus.OK;

public class VectorApiImpl implements VectorApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(VectorApiImpl.class);
    private RestHighLevelClient client;
    private ElasticsearchClient elasticsearchClient;
    private VectorSearchService vectorSearchService;
    private ElasticsearchMappingFetcher elasticsearchMappingFetcher;

    private ExpireService expireService;

    public VectorApiImpl() {
    }

    public VectorApiImpl(String serverUrl, String apiKey) {
        vectorSearchService = new VectorSearchService(serverUrl, apiKey);
        this.client = vectorSearchService.getRestHighLevelClient();
        this.elasticsearchClient = vectorSearchService.getEsClient();
        elasticsearchMappingFetcher = new ElasticsearchMappingFetcher(elasticsearchClient);
        expireService = new ExpireService(elasticsearchClient, elasticsearchMappingFetcher);
    }


    @Override
    public void test() {
        try {
            String indexName = "eco_test01";
            long expireTimeSeconds0 = elasticsearchMappingFetcher.getExpireTimeSeconds0(indexName);
            LOGGER.info("expireTimeSeconds0:{} test", expireTimeSeconds0);
            int dimension = elasticsearchMappingFetcher.getDimension(indexName);
            MetricType metricType = elasticsearchMappingFetcher.getMetricType(indexName);
            LOGGER.info("dimension:{} metricType:{} test", dimension, metricType);
        } catch (Exception e) {
            LOGGER.error("test error", e);
        }
    }

    @Override
    public CreateResponse createIndex(IndexConfig indexConfig, IndexSchema indexSchema) {
        //do some check
        String indexConfigErrorMsg = Validator.validateIndexConfig(indexConfig);
        if (StringUtils.isNotEmpty(indexConfigErrorMsg)) {
            return new CreateResponse(ERROR.getStatusCode(), STR."validateIndexConfig Error:\{indexConfigErrorMsg}");
        }
        CreateIndexRequest request = new CreateIndexRequest(indexConfig.getIndexName());
        String esSchemaJson;
        if (indexSchema == null) {
            indexSchema = new IndexSchema();
            indexSchema.setSchema(new ArrayList<>());
            indexSchema.setRouting(new ArrayList<>());
        }

        List<DataField> schema = indexSchema.getSchema();
        LOGGER.info("start createIndex,indexConfig:{},indexSchema:{}", JsonUtils.toJSON(indexConfig), JsonUtils.toJSON(schema));
        try {
            esSchemaJson = MappingGenerator.generateIndexMappingJson(schema, indexConfig);
        } catch (JsonProcessingException e) {
            return new CreateResponse(ERROR.getStatusCode(), STR."MappingGenerator.generateIndexMappingJson Error:\{e}");
        }
        //convert schema to es schema
        if (StringUtils.isEmpty(esSchemaJson)) {
            return new CreateResponse(ERROR.getStatusCode(), "MappingGenerator.generateIndexMappingJson Error");
        }
        request.mapping(esSchemaJson, XContentType.JSON);
        try {
            CreateIndexResponse response = client.indices().create(request, RequestOptions.DEFAULT);
            LOGGER.info("createIndex,mapping:{}", esSchemaJson);
            return new CreateResponse(response.isAcknowledged() ? OK.getStatusCode() : ERROR.getStatusCode(), response.isAcknowledged() ? "OK" : "Error");
        } catch (IOException e) {
            LOGGER.error("createIndex error,", e);
            return new CreateResponse(EXCEPTION.getStatusCode(), e.getMessage());
        }
    }

    @Override
    public UpdateResponse insert(String indexName, String id, float[] vectorData, Map<String, Object> profile) {
        IndexRequest request = new IndexRequest(indexName).id(id).opType(DocWriteRequest.OpType.CREATE);
        try {
            MetricType metricType = elasticsearchMappingFetcher.getMetricType(indexName);
            int dimension = elasticsearchMappingFetcher.getDimension(indexName);
            String errorMsg = Validator.validateInsertParameters(indexName, id, vectorData, profile, dimension);
            if (StringUtils.isNotEmpty(errorMsg)) {
                return new UpdateResponse(ERROR.getStatusCode(), STR."insert Error:\{errorMsg}");
            }
            if (profile == null) {
                profile = new HashMap<>();
            }
            if (metricType == MetricType.BOOL) {
                profile.put(Constants.BOOL_VECTOR_FIELD_NAME, VectorUtils.toVectorString(vectorData));
            } else {
                profile.put(Constants.VECTOR_FIELD_NAME, vectorData);
            }
            profile.put(Constants.DEFAULT_CREATE_TIME_FIELD, System.currentTimeMillis() / 1000L);
            request.source(profile);
            client.index(request, RequestOptions.DEFAULT);
            LOGGER.info("insert indexName:{},id:{},vectorData:{},profile:{}", indexName, id, vectorData, profile);
            return new UpdateResponse(OK.getStatusCode(), "OK");
        } catch (ElasticsearchStatusException e) {
            if (e.status() == RestStatus.CONFLICT) {
                return new UpdateResponse(ERROR.getStatusCode(), STR."Document with id [\{id}] already exists.");
            }
            return new UpdateResponse(EXCEPTION.getStatusCode(), e.getMessage());
        } catch (IOException e) {
            return new UpdateResponse(EXCEPTION.getStatusCode(), e.getMessage());
        }
    }

    @Override
    public BulkResponse bulkInsert(String indexName, List<Tuple3<String, float[], Map<String, Object>>> datas) {
        BulkRequest request = new BulkRequest();
        try {
            MetricType metricType = elasticsearchMappingFetcher.getMetricType(indexName);
            int dimension = elasticsearchMappingFetcher.getDimension(indexName);
            for (Tuple3<String, float[], Map<String, Object>> data : datas) {
                IndexRequest indexRequest = new IndexRequest(indexName).id(data.getF1());
                float[] vectorData = data.getF2();
                if (dimension != vectorData.length) {
                    return new BulkResponse(ERROR.getStatusCode(), "dimension not vectorData's length");
                }
                Map<String, Object> dataMap = data.getF3();
                if (dataMap == null) {
                    dataMap = new HashMap<>();
                }
                if (metricType == MetricType.BOOL) {
                    dataMap.put(Constants.BOOL_VECTOR_FIELD_NAME, VectorUtils.toVectorString(vectorData));
                } else {
                    dataMap.put(Constants.VECTOR_FIELD_NAME, vectorData);
                }
                dataMap.put(Constants.DEFAULT_CREATE_TIME_FIELD, System.currentTimeMillis() / 1000L);
                indexRequest.source(dataMap);
                request.add(indexRequest);
            }
            org.elasticsearch.action.bulk.BulkResponse response = client.bulk(request, RequestOptions.DEFAULT);
            if (response.hasFailures()) {
                return new BulkResponse(ERROR.getStatusCode(), STR."bulk insert error,\{response.buildFailureMessage()}");
            }
            LOGGER.info("bulkInsert indexName:{},data:{}", indexName, datas.size());
            return new BulkResponse(OK.getStatusCode());
        } catch (IOException e) {
            return new BulkResponse(ERROR.getStatusCode(), STR."bulk insert error,\{e.getCause()}");
        }
    }

    @Override
    public UpdateResponse update(String indexName, String id, float[] vectorData, Map<String, Object> profile) {
        UpdateRequest request = new UpdateRequest(indexName, id);
        try {
            MetricType metricType = elasticsearchMappingFetcher.getMetricType(indexName);
            int dimension = elasticsearchMappingFetcher.getDimension(indexName);
            String errorMsg = Validator.validateInsertParameters(indexName, id, vectorData, profile, dimension);
            if (StringUtils.isNotEmpty(errorMsg)) {
                return new UpdateResponse(ERROR.getStatusCode(), STR."update Error:\{errorMsg}");
            }
            if (profile == null) {
                profile = new HashMap<>();
            }
            if (metricType == MetricType.BOOL) {
                profile.put(Constants.BOOL_VECTOR_FIELD_NAME, VectorUtils.toVectorString(vectorData));
            } else {
                profile.put(Constants.VECTOR_FIELD_NAME, vectorData);
            }
            profile.put(Constants.DEFAULT_CREATE_TIME_FIELD, System.currentTimeMillis() / 1000L);
            request.doc(profile);
            client.update(request, RequestOptions.DEFAULT);
            LOGGER.info("update indexName:{},id:{},vectorData:{},profile:{}", indexName, id, vectorData, profile);
            return new UpdateResponse(OK.getStatusCode(), "OK");
        } catch (IOException e) {
            return new UpdateResponse(EXCEPTION.getStatusCode(), e.getCause().toString());
        }
    }

    @Override
    public UpdateResponse updateVector(String indexName, String id, float[] vectorData) {
        UpdateRequest request = new UpdateRequest(indexName, id);
        try {
            MetricType metricType = elasticsearchMappingFetcher.getMetricType(indexName);
            int dimension = elasticsearchMappingFetcher.getDimension(indexName);
            String errorMsg = Validator.validateInsertParameters(indexName, id, vectorData, dimension);
            if (StringUtils.isNotEmpty(errorMsg)) {
                return new UpdateResponse(ERROR.getStatusCode(), STR."updateVector Error:\{errorMsg}");
            }
            Map<String, Object> updateMap;
            if (metricType == MetricType.BOOL) {
                updateMap = Map.of(
                        Constants.BOOL_VECTOR_FIELD_NAME, VectorUtils.toVectorString(vectorData),
                        Constants.DEFAULT_CREATE_TIME_FIELD, System.currentTimeMillis() / 1000L
                );
            } else {
                updateMap = Map.of(
                        Constants.VECTOR_FIELD_NAME, vectorData,
                        Constants.DEFAULT_CREATE_TIME_FIELD, System.currentTimeMillis() / 1000L
                );
            }
            request.doc(updateMap);
            client.update(request, RequestOptions.DEFAULT);
            LOGGER.info("updateVector indexName:{},id:{},vectorData:{}", indexName, id, vectorData);
            return new UpdateResponse(OK.getStatusCode(), "OK");
        } catch (IOException e) {
            LOGGER.error("updateVector indexName:{},id:{},vectorData:{}", indexName, id, vectorData, e);
            return new UpdateResponse(EXCEPTION.getStatusCode(), e.getCause().toString());
        }
    }

    @Override
    public UpdateResponse updateProfile(String indexName, String id, Map<String, Object> profile) {
        Preconditions.checkState(StringUtils.isNotEmpty(indexName), "indexName is empty");
        Preconditions.checkState(StringUtils.isNotEmpty(id), "id is empty");
        UpdateRequest request = new UpdateRequest(indexName, id);
        profile.put(Constants.DEFAULT_CREATE_TIME_FIELD, System.currentTimeMillis() / 1000L);
        request.doc(profile);
        try {
            client.update(request, RequestOptions.DEFAULT);
            LOGGER.info("updateProfile indexName:{},id:{},profile:{}", indexName, id, profile);
            return new UpdateResponse(OK.getStatusCode(), "OK");
        } catch (IOException e) {
            LOGGER.error("updateProfile error,indexName:{} id:{} profile:{}", indexName, id, profile, e);
            return new UpdateResponse(EXCEPTION.getStatusCode(), e.getCause().toString());
        }
    }

    @Override
    public UpdateResponse delete(String indexName, String id) {
        DeleteRequest request = new DeleteRequest(indexName, id);
        try {
            Preconditions.checkState(StringUtils.isNotEmpty(indexName), "indexName is empty");
            Preconditions.checkState(StringUtils.isNotEmpty(id), "id is empty");
            client.delete(request, RequestOptions.DEFAULT);
            return new UpdateResponse(OK.getStatusCode(), "OK");
        } catch (IOException e) {
            LOGGER.error("delete error,indexName:{} id:{}", indexName, id, e);
            return new UpdateResponse(EXCEPTION.getStatusCode(), e.getCause().toString());
        }
    }

    @Override
    public VectorWithProfile searchById(String indexName, String id) {
        if (StringUtils.isEmpty(id)) {
            LOGGER.warn("searchById id is empty,indexName:{}", indexName);
            return null;
        }
        VectorWithProfile searchRes = vectorSearchService.search(indexName, id);
        return searchRes;
    }

    @Override
    public SearchResponse search(SearchRequest searchRequest) {
        String queryDsl = searchRequest.getQueryDsl();
        if (StringUtils.isEmpty(queryDsl)) {
            return search(searchRequest.getIndexName(), searchRequest.getSearchVector(), searchRequest.getLimit(),
                    searchRequest.getScoreGt(), searchRequest.getScoreLt());
        }
        return searchByDsl(searchRequest.getIndexName(), searchRequest.getSearchVector(), searchRequest.getLimit(),
                searchRequest.getScoreGt(), searchRequest.getScoreLt(), searchRequest.getQueryDsl());
    }

    public SearchResponse search(String indexName, float[] searchVector, int limit, Float scoreGt, Float scoreLt) {
        return doSearch(indexName, searchVector, limit, scoreGt, scoreLt, null);
    }

    public SearchResponse searchByDsl(String indexName, float[] searchVector, int limit, Float scoreGt, Float scoreLt, String queryDsl) {
        return doSearch(indexName, searchVector, limit, scoreGt, scoreLt, queryDsl);
    }

    private SearchResponse doSearch(String indexName, float[] searchVector, int limit, Float scoreGt, Float scoreLt, String queryDslJson) {
        SearchResponse result = new SearchResponse();
        SearchResponse validateRes = Validator.validateParam(indexName, searchVector, limit, scoreGt, scoreLt, result);
        if (validateRes != null) return validateRes;
        try {
            MetricType metricType = elasticsearchMappingFetcher.getMetricType(indexName);
            List<Hit<Map>> hits = searchHits(indexName, searchVector, limit, scoreGt, scoreLt, queryDslJson, metricType);
            List<Similar> similarItems = new ArrayList<>(hits.size());
            //默认都返回Profile，如果不需要后期可以传入条件
            boolean similarItemWithData = true;
            for (Hit<Map> hit : hits) {
                Similar item;
                if (similarItemWithData) {
                    Map<String, Object> source = (Map<String, Object>) hit.source();
                    Map<String, Object> dataMap = new HashMap<>(source.size());
                    for (Map.Entry<String, Object> entry : source.entrySet()) {
                        String key = entry.getKey();
                        if (!isNativeField.apply(key)) {
                            dataMap.put(key, entry.getValue());
                        }
                    }
                    item = new SimilarItemWithData(hit.id(), hit.score().floatValue(), dataMap);
                } else {
                    item = new SimilarItem(hit.id(), hit.score().floatValue());
                }
                similarItems.add(item);
            }
            result.setItems(similarItems);
            result.setStatus(OK.getStatusCode());
            return result;
        } catch (Exception e) {
            result.setStatus(ERROR.getStatusCode());
            result.setErrorCode(STR."error,\{e.getCause()}");
        }
        return result;
    }

    private List<Hit<Map>> searchHits(String indexName, float[] searchVector, int limit, Float scoreGt, Float scoreLt, String queryDslJson, MetricType metricType) {
        List<Hit<Map>> hits;
        if (metricType == MetricType.BOOL) {
            hits = vectorSearchService.searchBoolIndex(indexName, searchVector, limit, scoreGt, scoreLt, queryDslJson);
        } else {
            hits = vectorSearchService.search(indexName, searchVector, limit, scoreGt, scoreLt, queryDslJson);
        }
        return hits;
    }

    @Override
    public void cleanExpiredData(String indexName) {
        expireService.cleanExpireData(indexName);
    }
}