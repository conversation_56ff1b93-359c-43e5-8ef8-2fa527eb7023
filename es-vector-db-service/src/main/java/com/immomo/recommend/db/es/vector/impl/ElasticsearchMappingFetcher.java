package com.immomo.recommend.db.es.vector.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.indices.ElasticsearchIndicesClient;
import co.elastic.clients.elasticsearch.indices.GetMappingRequest;
import co.elastic.clients.elasticsearch.indices.GetMappingResponse;
import co.elastic.clients.elasticsearch.indices.get_mapping.IndexMappingRecord;
import co.elastic.clients.json.JsonData;
import com.immomo.recommend.db.es.vector.type.MetricType;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class ElasticsearchMappingFetcher {

    private static final Map<String, MetricType> METRIC_TYPE_CACHE = new HashMap<>();
    private static final Map<String, Integer> DIMENSION_CACHE = new HashMap<>();
    private static final Map<String, Long> EXPIRE_TIME_SECONDS_CACHE = new HashMap<>();

    private final ElasticsearchClient elasticsearchClient;

    public ElasticsearchMappingFetcher(ElasticsearchClient elasticsearchClient) {
        this.elasticsearchClient = elasticsearchClient;
    }

    public IndexMappingRecord getMapping(String indexName) throws IOException {
        ElasticsearchIndicesClient indices = elasticsearchClient.indices();
        GetMappingRequest mappingRequest = new GetMappingRequest.Builder().index(indexName).build();
        GetMappingResponse mapping = indices.getMapping(mappingRequest);
        IndexMappingRecord indexMappingRecord = mapping.get(indexName);
        return indexMappingRecord;
    }


    public Map<String, JsonData> getMeta(String indexName) throws IOException {
        ElasticsearchIndicesClient indices = elasticsearchClient.indices();
        GetMappingRequest mappingRequest = new GetMappingRequest.Builder().index(indexName).build();
        GetMappingResponse mapping = indices.getMapping(mappingRequest);
        IndexMappingRecord indexMappingRecord = mapping.get(indexName);
        Map<String, JsonData> meta = indexMappingRecord.mappings().meta();
        return meta;
    }

    //implement a getIndexTypeByCache
    public MetricType getMetricType(String indexName) throws IOException {
        if (METRIC_TYPE_CACHE.containsKey(indexName)) {
            return METRIC_TYPE_CACHE.get(indexName);
        }
        MetricType metricType = getMetricType0(indexName);
        METRIC_TYPE_CACHE.put(indexName, metricType);
        return metricType;
    }


    //implement a getDimensionByCache
    public int getDimension(String indexName) throws IOException {
        if (DIMENSION_CACHE.containsKey(indexName)) {
            return DIMENSION_CACHE.get(indexName);
        }
        int dimension = getDimension0(indexName);
        DIMENSION_CACHE.put(indexName, dimension);
        return dimension;
    }

    public long getExpireTimeSeconds(String indexName) throws IOException {
        if (EXPIRE_TIME_SECONDS_CACHE.containsKey(indexName)) {
            return EXPIRE_TIME_SECONDS_CACHE.get(indexName);
        }
        long expireTimeSeconds = getExpireTimeSeconds0(indexName);
        if (expireTimeSeconds > 0) {
            EXPIRE_TIME_SECONDS_CACHE.put(indexName, expireTimeSeconds);
        }
        return expireTimeSeconds;
    }


    private int getDimension0(String indexName) throws IOException {
        Map<String, JsonData> meta = getMeta(indexName);
        return meta.get("dimension").to(Integer.class);
    }


    private MetricType getMetricType0(String indexName) throws IOException {
        Map<String, JsonData> meta = getMeta(indexName);
        return MetricType.valueOf(meta.get("metricType").to(String.class));
    }

    public long getExpireTimeSeconds0(String indexName) throws IOException {
        Map<String, JsonData> meta = getMeta(indexName);
        return meta.get("expireTimeSeconds").to(Long.class);
    }
}
