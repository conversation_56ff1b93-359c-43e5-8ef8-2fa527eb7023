package com.immomo.recommend.db.es.vector.impl.utils;

import com.google.common.base.Joiner;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/02/27 16:03
 */
public class VectorUtils {

    public static String toVectorString(float[] vector) {
        //concat vector item with space
        List<Float> embeddingArray = new ArrayList<>(vector.length);
        for (float v : vector) {
            embeddingArray.add(v);
        }
        String join = Joiner.on(" ").join(embeddingArray);
        return join;
    }

    public static float[] toVector(String vectorStr) {
        if (StringUtils.isEmpty(vectorStr)) {
            return new float[0];
        }
        String[] split = StringUtils.split(vectorStr, " ");
        float[] vector = new float[split.length];
        for (int i = 0; i < split.length; i++) {
            vector[i] = Float.parseFloat(split[i]);
        }
        return vector;
    }
}
