package com.immomo.recommend.db.es.vector.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.immomo.recommend.db.es.vector.config.DataField;
import com.immomo.recommend.db.es.vector.config.IndexConfig;
import com.immomo.recommend.db.es.vector.type.MetricType;

import java.util.List;

import static com.immomo.recommend.db.es.vector.impl.Constants.BOOL_VECTOR_FIELD_NAME;
import static com.immomo.recommend.db.es.vector.impl.Constants.DEFAULT_CREATE_TIME_FIELD;
import static com.immomo.recommend.db.es.vector.impl.Constants.VECTOR_FIELD_NAME;
import static com.immomo.recommend.db.es.vector.impl.Constants.isNativeField;

public class MappingGenerator {
    public static final String INDEX_TYPE_MAPPING_KEY = "type";

    private static final ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
    }

    public static String generateIndexMappingJson(List<DataField> schema, IndexConfig indexConfig)
            throws JsonProcessingException {
        ObjectNode mappingNode = objectMapper.createObjectNode();
        ObjectNode propertiesNode = objectMapper.createObjectNode();
        ObjectNode metaNode = objectMapper.createObjectNode();
        metaNode.put("indexType", indexConfig.getIndexType().name());
        metaNode.put("metricType", indexConfig.getMetricType().name());
        metaNode.put("shard", indexConfig.getShard());
        metaNode.put("dimension", indexConfig.getDimension());
        metaNode.put("expireTimeSeconds", indexConfig.getExpireTimeSeconds());
        mappingNode.set("_meta", metaNode);
        for (DataField dataField : schema) {
            ObjectNode fieldNode = objectMapper.createObjectNode();
            String fieldName = dataField.getField();
            if (isNativeField.apply(fieldName)) {
                throw new IllegalArgumentException(fieldName + " is default keyword");
            }
            switch (dataField.getType()) {
                case INT:
                    fieldNode.put(INDEX_TYPE_MAPPING_KEY, "integer");
                    break;
                case LONG:
                    fieldNode.put(INDEX_TYPE_MAPPING_KEY, "long");
                    break;
                case FLOAT:
                    fieldNode.put(INDEX_TYPE_MAPPING_KEY, "float");
                    break;
                case DOUBLE:
                    fieldNode.put(INDEX_TYPE_MAPPING_KEY, "double");
                    break;
                case STRING:
                    fieldNode.put(INDEX_TYPE_MAPPING_KEY, "keyword");
                    break;
                case BOOL:
                    fieldNode.put(INDEX_TYPE_MAPPING_KEY, "boolean");
                    break;
                case GEO_POINT:
                    fieldNode.put(INDEX_TYPE_MAPPING_KEY, "geo_point");
                    break;
                case ARRAY:
                    fieldNode.put(INDEX_TYPE_MAPPING_KEY, "keyword");
                    break;
                default:
                    throw new IllegalArgumentException("Unknown field type: " + dataField.getType());
            }
            propertiesNode.set(fieldName, fieldNode);
        }
        MetricType metricType = indexConfig.getMetricType();
        if (metricType == MetricType.BOOL) {
            //成都特殊需求
            ObjectNode vectorFieldNode = objectMapper.createObjectNode();
            vectorFieldNode.put("type", "text");
            vectorFieldNode.put("analyzer", "whitespace");
            vectorFieldNode.put("similarity", "boolean");
            propertiesNode.set(BOOL_VECTOR_FIELD_NAME, vectorFieldNode);
        } else {
            // 添加默认的 vector 字段
            ObjectNode vectorFieldNode = objectMapper.createObjectNode();
            vectorFieldNode.put("type", "dense_vector");
            int dimension = indexConfig.getDimension();
            vectorFieldNode.put("dims", dimension);
            vectorFieldNode.put("similarity", metricType.getEsAlias());
            propertiesNode.set(VECTOR_FIELD_NAME, vectorFieldNode);
        }
        //默认添加一个时间字段
        ObjectNode createTimeNode = objectMapper.createObjectNode();
        createTimeNode.put(INDEX_TYPE_MAPPING_KEY, "long");
        propertiesNode.set(DEFAULT_CREATE_TIME_FIELD, createTimeNode);
        //mapping
        mappingNode.set("properties", propertiesNode);
        return objectMapper
                .writerWithDefaultPrettyPrinter()
                .writeValueAsString(mappingNode);
    }
}
