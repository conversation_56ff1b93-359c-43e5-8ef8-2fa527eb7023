package com.immomo.recommend.db.es.vector.impl.service;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.NumberRangeQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.elasticsearch.core.DeleteByQueryRequest;
import co.elastic.clients.elasticsearch.core.DeleteByQueryResponse;
import com.immomo.recommend.db.es.vector.impl.Constants;
import com.immomo.recommend.db.es.vector.impl.ElasticsearchMappingFetcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/03/03 17:38
 */
public class ExpireService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExpireService.class);
    private final ElasticsearchClient esClient;
    private ElasticsearchMappingFetcher elasticsearchMappingFetcher;

    public ExpireService(ElasticsearchClient esClient, ElasticsearchMappingFetcher elasticsearchMappingFetcher) {
        this.esClient = esClient;
        this.elasticsearchMappingFetcher = elasticsearchMappingFetcher;
    }

    public void cleanExpireData(String indexName) {
        try {
            Long expireTimeSeconds = elasticsearchMappingFetcher.getExpireTimeSeconds(indexName);
            if (expireTimeSeconds == null || expireTimeSeconds <= 0) {
                return;
            }
            Long expireAt = System.currentTimeMillis() - expireTimeSeconds;
            RangeQuery rangeQuery = NumberRangeQuery.of(
                    n -> n.field(Constants.DEFAULT_CREATE_TIME_FIELD).lte(expireAt.doubleValue()))._toRangeQuery();
            DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest.Builder()
                    .index(indexName).query(rangeQuery._toQuery()).build();
            DeleteByQueryResponse deleteByQueryResponse = esClient.deleteByQuery(deleteByQueryRequest);
            String response = deleteByQueryResponse.toString();
            LOGGER.info("index:{},expireTimeSeconds:{},deleteByQueryResponse:{}", indexName, expireTimeSeconds, response);
        } catch (IOException e) {
            LOGGER.error("cleanExpireData error,indexName:{}", indexName, e);
        }
    }
}
