package com.immomo.recommend.db.es.vector.impl;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2025/02/20 14:35
 */
public interface Constants {
    String VECTOR_FIELD_NAME = "vector";
    String BOOL_VECTOR_FIELD_NAME = "boolVector";
    String DEFAULT_CREATE_TIME_FIELD = "_ctime";
    String DEFAULT_META_DATA_FIELD = "_meta";
    Function<String, Boolean> isNativeField = string ->
            VECTOR_FIELD_NAME.equals(string) ||
            BOOL_VECTOR_FIELD_NAME.equals(string) ||
            DEFAULT_CREATE_TIME_FIELD.equals(string) ||
            DEFAULT_META_DATA_FIELD.equals(string);
}
