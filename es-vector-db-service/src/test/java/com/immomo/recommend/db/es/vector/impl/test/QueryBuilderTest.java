package com.immomo.recommend.db.es.vector.impl.test;

import com.immomo.recommend.db.es.vector.impl.utils.QueryUtils;
import com.immomo.recommend.db.es.vector.query.builder.DistanceQuery;
import com.immomo.recommend.db.es.vector.query.builder.QueryBuilder;
import com.immomo.recommend.db.es.vector.query.builder.RangeQuery;
import com.immomo.recommend.db.es.vector.query.builder.TermQuery;
import com.immomo.recommend.db.es.vector.query.builder.TermsQuery;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/02/27 14:37
 */
public class QueryBuilderTest {
    private String builderJson;
    private QueryBuilder boolBuilder;

    @Before
    public void before() {
        boolBuilder = new QueryBuilder();
        boolBuilder.add(new TermQuery("age", 18));
        boolBuilder.add(new TermQuery("gender", 1));
        boolBuilder.add(new RangeQuery("age", 19, 35));
        List<Object> codeLanguages = new ArrayList<>();
        codeLanguages.add(1.5f);
        codeLanguages.add(2f);
        codeLanguages.add(3f);
        boolBuilder.add(new TermsQuery("someTermKey", codeLanguages));
        boolBuilder.add(new DistanceQuery("location", 30, "km", 39.99, 116.39));
        builderJson = boolBuilder.buildToJson();
    }

    @Test
    public void test() throws Exception {
        QueryBuilder queryBuilder = QueryUtils.fromJson(builderJson);
        String builderJsonDeserialized = queryBuilder.buildToJson();
        Assert.assertEquals(builderJsonDeserialized, builderJson);
        Assert.assertNotEquals(queryBuilder, boolBuilder);
    }
}
