package com.immomo.recommend.db.es.vector.impl.test;

import com.immomo.mcf.util.JsonUtils;
import com.immomo.recommend.db.es.vector.bean.SearchResponse;
import com.immomo.recommend.db.es.vector.bean.VectorWithProfile;
import com.immomo.recommend.db.es.vector.config.DataField;
import com.immomo.recommend.db.es.vector.config.IndexConfig;
import com.immomo.recommend.db.es.vector.config.IndexSchema;
import com.immomo.recommend.db.es.vector.impl.VectorApiImpl;
import com.immomo.recommend.db.es.vector.query.builder.QueryBuilder;
import com.immomo.recommend.db.es.vector.query.builder.RangeQuery;
import com.immomo.recommend.db.es.vector.type.FieldType;
import com.immomo.recommend.db.es.vector.type.IndexType;
import com.immomo.recommend.db.es.vector.type.MetricType;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/02/20 19:10
 */
public class VectorUnitTest {

    @Test
    public void test() {
        String indexName = "vector_test02";
        extracted(indexName);
    }

    @Test
    public void test2() {
        String indexName = "vector_test03";
        extracted(indexName);
    }

    private static void extracted(String indexName) {
        String serverUrl = "http://172.16.108.23:9200";
        String apiKey = "RzNMRko1VUJHVzZCR0F5RXRGY0g6cS03VktqSG1TOXFiYXBmX2FIS0J4dw==";
        VectorApiImpl vectorApi = new VectorApiImpl(serverUrl, apiKey);
        IndexConfig indexConfig = new IndexConfig();
        indexConfig.setIndexName(indexName);
        indexConfig.setIndexType(IndexType.FLAT);
        indexConfig.setMetricType(MetricType.COSINE);
        indexConfig.setShard(1);
        indexConfig.setDimension(4);
        //设置过期时间
        indexConfig.setExpireTimeSeconds(TimeUnit.SECONDS.toDays(100));
        IndexSchema indexSchema = new IndexSchema();
        List<DataField> dataFields = new ArrayList<>();
        dataFields.add(new DataField("name", FieldType.STRING, null));
        dataFields.add(new DataField("age", FieldType.INT, null));
        indexSchema.setSchema(dataFields);
//
//        vectorApi.createIndex(indexConfig, indexSchema);
//
//        Map<String, Object> dataMap = new HashMap<>();
//        dataMap.put("name", "Jack");
//        dataMap.put("age", 18);
//        //all vector data are range 0~1 128 dimension
//        float[] vectorData = getVectorData();
//        vectorApi.insert(indexName, "bk00" + 1, vectorData, dataMap);
//        System.out.println("bk001:"+ JsonUtils.toJSON(dataMap));
//        VectorWithProfile vectorWithProfile = vectorApi.searchById(indexName, "bk00" + 1);
//        System.out.println("bk001:"+JsonUtils.toJSON(vectorWithProfile));
//        dataMap.put("name", "Tom");
//        vectorApi.update(indexName, "bk00" + 1, getFixedVectorData(), dataMap);
//        vectorApi.updateProfile(indexName, "bk00" + 1, dataMap);
//
//        vectorApi.updateVector(indexName, "bk00" + 1, getVectorData());
//
//        ArrayList<Tuple3<String, float[], Map<String, Object>>> datas = new ArrayList<>();
//        for (int i = 0; i < 3; i++) {
//            Map<String, Object> map = new HashMap<>();
//            map.put("name", "Jack" + i);
//            map.put("age", 18 + i);
//            datas.add(new Tuple3<>("ck00" + i, getVectorData(), map));
//        }
//        vectorApi.bulkInsert(indexName, datas);
        // vectorApi.delete(indexName, "ak0098");
        VectorWithProfile search = vectorApi.searchById(indexName, "ck00" + 1);
        float[] searchVector = {0.30122292f, 0.01f, 0.01f, 0.01f};
        QueryBuilder builder = new QueryBuilder();
        builder.add(new RangeQuery("age", 18, 32));
        SearchResponse searchResponse = vectorApi.searchByDsl(indexName, searchVector, 10, null, null, builder.buildToJson());
        System.out.println(JsonUtils.prettyPrint(searchResponse));
    }

    private static float[] getVectorData() {
        float[] vectorData = new float[4];
        for (int i = 0; i < 4; i++) {
            vectorData[i] = (float) Math.random();
        }
        return vectorData;
    }

    private static float[] getFixedVectorData() {
        float[] vectorData = new float[4];
        for (int i = 0; i < 4; i++) {
            vectorData[i] = (float) 0.01;
        }
        return vectorData;
    }
}
