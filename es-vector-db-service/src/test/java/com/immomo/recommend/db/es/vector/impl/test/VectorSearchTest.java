package com.immomo.recommend.db.es.vector.impl.test;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.JsonData;
import com.immomo.recommend.db.es.vector.impl.ElasticsearchMappingFetcher;
import com.immomo.recommend.db.es.vector.impl.service.VectorSearchService;
import com.immomo.recommend.db.es.vector.query.builder.QueryBuilder;
import com.immomo.recommend.db.es.vector.query.builder.RangeQuery;
import org.junit.Test;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/02/24 18:57
 */
public class VectorSearchTest {

    @Test
    public void test() throws IOException {
        String serverUrl = "http://172.16.108.23:9200";
        String apiKey = "RzNMRko1VUJHVzZCR0F5RXRGY0g6cS03VktqSG1TOXFiYXBmX2FIS0J4dw==";
        VectorSearchService vectorSearchService = new VectorSearchService(serverUrl, apiKey);
        float[] searchVector = {0.30122292f, 0.01f, 0.01f, 0.01f};
        QueryBuilder builder = new QueryBuilder();
        builder.add(new RangeQuery("age",18 ,32));
        List<Hit<Map>> vectorTest02 = vectorSearchService.searchBoolIndex("vector_test02", searchVector, 2, 2.0f, null, builder.buildToJson());
        System.out.println(vectorTest02);
        ElasticsearchClient esClient = vectorSearchService.getEsClient();
        ElasticsearchMappingFetcher fetcher = new ElasticsearchMappingFetcher(esClient);
        Map<String, JsonData> meta = fetcher.getMeta("vector_test02");
        JsonData expireTimeSeconds = meta.get("expireTimeSeconds");

        System.out.println(expireTimeSeconds);
    }


    @Test
    public void test2() throws IOException {
        String serverUrl = "http://172.16.108.23:9200";
        String apiKey = "RzNMRko1VUJHVzZCR0F5RXRGY0g6cS03VktqSG1TOXFiYXBmX2FIS0J4dw==";
        VectorSearchService vectorSearchService = new VectorSearchService(serverUrl, apiKey);
        float[] searchVector = {0.05368831f, 0.6599018f, 0.8229311f, 0.33234814f};
        QueryBuilder builder = new QueryBuilder();
        builder.add(new RangeQuery("age", 18,32));
        List<Hit<Map>> vectorTest03 = vectorSearchService.search("vector_test03", searchVector, 20, null, null, null);
        System.out.println(vectorTest03);
        ElasticsearchClient esClient = vectorSearchService.getEsClient();
        ElasticsearchMappingFetcher fetcher = new ElasticsearchMappingFetcher(esClient);
        fetcher.getMapping("vector_test02");
    }
}
