package com.immomo.recommend.db.es.vector.impl.test;

import co.elastic.clients.elasticsearch._types.query_dsl.NumberRangeQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.elasticsearch.core.DeleteByQueryRequest;
import com.immomo.mcf.util.JsonUtils;
import com.immomo.recommend.db.es.vector.config.IndexConfig;
import com.immomo.recommend.db.es.vector.type.IndexType;
import com.immomo.recommend.db.es.vector.type.MetricType;
import org.junit.Test;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/03/03 10:57
 */
public class JsonTest {

    @Test
    public void test(){
        IndexConfig indexConfig = new IndexConfig();
        indexConfig.setIndexName("eco_test01");
        indexConfig.setIndexType(IndexType.FLAT);
        indexConfig.setMetricType(MetricType.COSINE);
        indexConfig.setShard(1);
        indexConfig.setDimension(4);
        //设置过期时间
        indexConfig.setExpireTimeSeconds(TimeUnit.SECONDS.toDays(100));
        System.out.println(JsonUtils.prettyPrint(indexConfig));
    }

    @Test
    public void test2(){
        RangeQuery rangeQuery = NumberRangeQuery.of(n -> n.field("_ctime").lte(111d))._toRangeQuery();
        System.out.println(rangeQuery.toString());
        DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest.Builder().index("indexName").query(rangeQuery._toQuery()).build();
        System.out.println(deleteByQueryRequest.toString());
    }
}
