package com.immomo.recommend.db.es.vector.impl.test;

import com.immomo.recommend.db.es.vector.config.IndexConfig;
import com.immomo.recommend.db.es.vector.impl.VectorApiImpl;
import com.immomo.recommend.db.es.vector.type.IndexType;
import com.immomo.recommend.db.es.vector.type.MetricType;
import org.junit.Test;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/03/03 10:59
 */
public class EmptySchemaTest {

    @Test
    public void test(){
        String indexName="test_empty01";
        String serverUrl = "http://172.16.108.23:9200";
        String apiKey = "RzNMRko1VUJHVzZCR0F5RXRGY0g6cS03VktqSG1TOXFiYXBmX2FIS0J4dw==";
        VectorApiImpl vectorApi = new VectorApiImpl(serverUrl, apiKey);
        IndexConfig indexConfig = new IndexConfig();
        indexConfig.setIndexName(indexName);
        indexConfig.setIndexType(IndexType.FLAT);
        indexConfig.setMetricType(MetricType.COSINE);
        indexConfig.setShard(1);
        indexConfig.setDimension(4);
        //设置过期时间
        indexConfig.setExpireTimeSeconds(TimeUnit.SECONDS.toDays(100));
        //CreateResponse index = vectorApi.createIndex(indexConfig, null);
       // System.out.println(index);


        vectorApi.insert(indexName, "so00" + 1, getVectorData(), null);
    }

    private static float[] getVectorData() {
        float[] vectorData = new float[4];
        for (int i = 0; i < 4; i++) {
            vectorData[i] = (float) Math.random();
        }
        return vectorData;
    }

    private static float[] getFixedVectorData() {
        float[] vectorData = new float[4];
        for (int i = 0; i < 4; i++) {
            vectorData[i] = (float) 0.01;
        }
        return vectorData;
    }
}
