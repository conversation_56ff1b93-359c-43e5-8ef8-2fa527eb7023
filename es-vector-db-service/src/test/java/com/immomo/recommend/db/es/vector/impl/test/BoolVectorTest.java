package com.immomo.recommend.db.es.vector.test;

import com.immomo.recommend.db.es.vector.bean.SearchResponse;
import com.immomo.recommend.db.es.vector.impl.VectorApiImpl;
import com.immomo.recommend.db.es.vector.request.SearchRequest;
import com.immomo.recommend.db.es.vector.type.MetricType;
import org.junit.Test;

/**
 * 布尔向量相似度测试
 * 用于验证布尔向量查询是否返回正确的相似度得分
 */
public class BoolVectorTest {
    
    @Test
    public void testBoolVectorSimilarity() {
        // 你的测试数据
        String indexName = "carina_ticket_data_user_feature_test";
        String targetId = "17289435648";
        
        // 数据库中的向量
        float[] dbVector = {0.2f, 0.1f, 0.1f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.1f, 0.2f, 0.2f, 5.1f, 1.1f, 0.2f, 6.1f, 0.3f, 0.1f, 0.3f, 0.07f, 0.3f, 0.1f, 0.5f, 0.2f, 0.1f, 1.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.2f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 4.1f, 0.2f, 0.2f, 0.2f, 0.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 0.2f, 0.1f, 0.1f, 0.1f, 0.2f, 0.2f, 0.2f, 0.1f, 0.1f, 0.1f, 0.2f, 0.1f, 0.1f, 0.0f, 13045.1f, 15.0001f, 27.2f};
        
        // 查询向量
        float[] queryVector = {0.2f, 0.2f, 0.2f, 0.1f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.1f, 0.1f, 0.2f, 0.1f, 0.2f, 0.1f, 0.3f, 0.07f, 0.3f, 0.3f, 0.5f, 0.2f, 0.1f, 1.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.2f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 0.2f, 0.1f, 0.1f, 0.1f, 0.2f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.2f, 0.1f, 0.1f, 0f, 13464.1f, 15.0001f, 27.2f};
        
        // 手动计算期望的相似度（匹配的元素个数）
        int expectedSimilarity = calculateBoolSimilarity(dbVector, queryVector);
        System.out.println("手动计算的相似度: " + expectedSimilarity);
        
        // 使用系统API进行查询
        VectorApiImpl vectorApi = createVectorApi(); // 你需要根据实际情况创建
        
        SearchRequest searchRequest = new SearchRequest(
            indexName,
            queryVector,
            10,
            null,
            null,
            null
        );
        
        SearchResponse response = vectorApi.search(searchRequest);
        
        // 验证结果
        if (response.getItems() != null && !response.getItems().isEmpty()) {
            response.getItems().forEach(item -> {
                if (targetId.equals(item.getId())) {
                    System.out.println("系统返回的得分: " + item.getScore());
                    System.out.println("期望得分: " + expectedSimilarity);
                    
                    // 验证得分是否匹配
                    if (Math.abs(item.getScore() - expectedSimilarity) < 0.001) {
                        System.out.println("✅ 得分匹配！布尔向量相似度计算正确");
                    } else {
                        System.out.println("❌ 得分不匹配！可能存在问题");
                    }
                }
            });
        }
    }
    
    /**
     * 手动计算布尔向量相似度（匹配元素个数）
     */
    private int calculateBoolSimilarity(float[] vector1, float[] vector2) {
        if (vector1.length != vector2.length) {
            return 0;
        }
        
        int matches = 0;
        for (int i = 0; i < vector1.length; i++) {
            if (Float.compare(vector1[i], vector2[i]) == 0) {
                matches++;
            }
        }
        return matches;
    }
    
    /**
     * 创建VectorApi实例 - 你需要根据实际配置修改这个方法
     */
    private VectorApiImpl createVectorApi() {
        // 这里需要根据你的实际配置来创建VectorApiImpl实例
        // 包括Elasticsearch连接配置等
        throw new UnsupportedOperationException("请根据实际配置实现此方法");
    }
    
    /**
     * 测试索引的MetricType配置
     */
    @Test
    public void testIndexMetricType() {
        String indexName = "carina_ticket_data_user_feature_test";
        VectorApiImpl vectorApi = createVectorApi();
        
        try {
            // 这里需要访问ElasticsearchMappingFetcher来检查MetricType
            // MetricType metricType = vectorApi.getElasticsearchMappingFetcher().getMetricType(indexName);
            // System.out.println("索引 " + indexName + " 的MetricType: " + metricType);
            
            // 如果MetricType不是BOOL，那就是问题所在
            System.out.println("请检查索引的MetricType配置是否为BOOL");
        } catch (Exception e) {
            System.err.println("检查MetricType时出错: " + e.getMessage());
        }
    }
}
