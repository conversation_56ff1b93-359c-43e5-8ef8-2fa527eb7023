/**
 * 布尔向量相似度计算器
 * 用于验证你的相似度计算是否正确
 */
public class BoolVectorSimilarityCalculator {
    
    public static void main(String[] args) {
        // 你提供的数据
        float[] dbVector = {0.2f, 0.1f, 0.1f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.1f, 0.2f, 0.2f, 5.1f, 1.1f, 0.2f, 6.1f, 0.3f, 0.1f, 0.3f, 0.07f, 0.3f, 0.1f, 0.5f, 0.2f, 0.1f, 1.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.2f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 4.1f, 0.2f, 0.2f, 0.2f, 0.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 0.2f, 0.1f, 0.1f, 0.1f, 0.2f, 0.2f, 0.2f, 0.1f, 0.1f, 0.1f, 0.2f, 0.1f, 0.1f, 0.0f, 13045.1f, 15.0001f, 27.2f};
        
        float[] queryVector = {0.2f, 0.2f, 0.2f, 0.1f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.2f, 0.1f, 0.1f, 0.2f, 0.1f, 0.2f, 0.1f, 0.3f, 0.07f, 0.3f, 0.3f, 0.5f, 0.2f, 0.1f, 1.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.2f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 1.1f, 0.2f, 0.1f, 0.1f, 0.1f, 0.2f, 0.1f, 0.1f, 0.1f, 0.1f, 0.1f, 0.2f, 0.1f, 0.1f, 0f, 13464.1f, 15.0001f, 27.2f};
        
        System.out.println("数据库向量长度: " + dbVector.length);
        System.out.println("查询向量长度: " + queryVector.length);
        
        // 计算相似度
        int similarity = calculateBoolSimilarity(dbVector, queryVector);
        System.out.println("布尔向量相似度（匹配元素个数）: " + similarity);
        
        // 详细分析差异
        analyzeVectorDifferences(dbVector, queryVector);
        
        // 验证你说的57是否正确
        if (similarity == 57) {
            System.out.println("✅ 计算结果与你提到的57匹配！");
        } else {
            System.out.println("❌ 计算结果与你提到的57不匹配，实际为: " + similarity);
        }
        
        System.out.println("\n=== 结论 ===");
        System.out.println("正确的布尔向量相似度得分应该是: " + similarity);
        System.out.println("你通过Elasticsearch查询得到的74是文本匹配的TF-IDF得分，不是向量相似度");
        System.out.println("要获得正确的相似度得分，请使用系统提供的API而不是直接的Elasticsearch查询");
    }
    
    /**
     * 计算布尔向量相似度（匹配元素个数）
     * 这与VectorSearchService.searchBoolIndex中的Painless脚本逻辑相同
     */
    private static int calculateBoolSimilarity(float[] vector1, float[] vector2) {
        if (vector1.length != vector2.length) {
            System.err.println("向量长度不匹配！");
            return 0;
        }
        
        int matches = 0;
        for (int i = 0; i < vector1.length; i++) {
            if (Float.compare(vector1[i], vector2[i]) == 0) {
                matches++;
            }
        }
        return matches;
    }
    
    /**
     * 分析向量差异
     */
    private static void analyzeVectorDifferences(float[] vector1, float[] vector2) {
        System.out.println("\n=== 向量差异分析 ===");
        int matches = 0;
        int differences = 0;
        
        for (int i = 0; i < Math.min(vector1.length, vector2.length); i++) {
            if (Float.compare(vector1[i], vector2[i]) == 0) {
                matches++;
            } else {
                differences++;
                if (differences <= 10) { // 只显示前10个差异
                    System.out.printf("位置 %d: DB=%.4f, Query=%.4f\n", i, vector1[i], vector2[i]);
                }
            }
        }
        
        System.out.println("匹配元素: " + matches);
        System.out.println("不匹配元素: " + differences);
        System.out.println("总元素: " + vector1.length);
        System.out.printf("匹配率: %.2f%%\n", (matches * 100.0) / vector1.length);
    }
}
