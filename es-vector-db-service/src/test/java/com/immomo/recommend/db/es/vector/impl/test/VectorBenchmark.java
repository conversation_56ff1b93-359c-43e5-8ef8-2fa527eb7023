package com.immomo.recommend.db.es.vector.impl.test;

import com.immomo.mcf.util.JsonUtils;
import com.immomo.recommend.db.es.vector.bean.BulkResponse;
import com.immomo.recommend.db.es.vector.bean.SearchResponse;
import com.immomo.recommend.db.es.vector.bean.Tuple3;
import com.immomo.recommend.db.es.vector.bean.VectorWithProfile;
import com.immomo.recommend.db.es.vector.impl.VectorApiImpl;
import com.immomo.recommend.db.es.vector.query.builder.QueryBuilder;
import com.immomo.recommend.db.es.vector.query.builder.TermQuery;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/02/20 19:10
 */
public class VectorBenchmark {
    private VectorApiImpl vectorApi;
    private String indexName = "vector_test04";

    @Before
    public void before() {
        String serverUrl = "http://172.16.108.23:9200";
        String apiKey = "RzNMRko1VUJHVzZCR0F5RXRGY0g6cS03VktqSG1TOXFiYXBmX2FIS0J4dw==";
        vectorApi = new VectorApiImpl(serverUrl, apiKey);

//        IndexConfig indexConfig = new IndexConfig();
//        indexConfig.setIndexName(indexName);
//        indexConfig.setIndexType(IndexType.FLAT);
//        indexConfig.setMetricType(MetricType.COSINE);
//        indexConfig.setShard(1);
//        indexConfig.setDimension(128);
//        //设置过期时间
//        indexConfig.setExpireTimeSeconds(TimeUnit.SECONDS.toDays(100));
//        IndexSchema indexSchema = new IndexSchema();
//        List<DataField> dataFields = new ArrayList<>();
//        dataFields.add(new DataField("name", FieldType.STRING, null));
//        dataFields.add(new DataField("age", FieldType.INT, null));
//        indexSchema.setSchema(dataFields);
        //vectorApi.createIndex(indexConfig, indexSchema);
    }

    @Test
    public void test() {
//        extracted(indexName);
        float[] vectorData = getVectorData();
        testSearch(vectorData);
        for (int i = 0; i < 100; i++) {
            testSearch(vectorData);
        }
    }

    //testSearchById
    private void testSearchById(){
        // search by id
        String id="dd";
        long time=System.currentTimeMillis();
        VectorWithProfile vectorWithProfile = vectorApi.searchById("", id);
        //print vectorWithProfile
    }

    public void testSearch(float[] vectorData) {
        ///vectorData = getVectorData();
        long start=System.currentTimeMillis();
        QueryBuilder queryBuilder=new QueryBuilder();
        queryBuilder.add(new TermQuery("age",77));
        SearchResponse search = vectorApi.searchByDsl(indexName, vectorData, 10, null, null,queryBuilder.buildToJson());
        System.out.println(search.getItems().get(0).getId());
        System.out.println(System.currentTimeMillis()-start);
    }

    private void extracted(String indexName) {
        ArrayList<Tuple3<String, float[], Map<String, Object>>> datas = new ArrayList<>();
        for (int i = 0; i < 5000; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("name", "Jirk" + i);
            map.put("age", RandomUtils.nextInt(18,100));
            datas.add(new Tuple3<>("dd00" + i, getVectorData(), map));
        }
        BulkResponse bulkResponse = vectorApi.bulkInsert(indexName, datas);
        System.out.println(JsonUtils.toJSON(bulkResponse));
    }

    private static float[] getVectorData() {
        float[] vectorData = new float[128];
        for (int i = 0; i < 128; i++) {
            vectorData[i] = RandomUtils.nextFloat(0,1);
        }
        return vectorData;
    }

    private static float[] getFixedVectorData() {
        float[] vectorData = new float[4];
        for (int i = 0; i < 4; i++) {
            vectorData[i] = (float) 0.01;
        }
        return vectorData;
    }
}
