package com.immomo.recommend.db.es.vector.query.builder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.ArrayList;
import java.util.List;

public class QueryBuilder {
    private final List<Query> queries = new ArrayList<>();

    public void add(Query query) {
        queries.add(query);
    }

    public ObjectNode build() {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode boolNode = mapper.createObjectNode();
        ArrayNode filterArray = mapper.createArrayNode();
        ObjectNode filterNode = mapper.createObjectNode();
        ArrayNode mustArray = mapper.createArrayNode();
        for (Query query : queries) {
            if (query instanceof MustMatchQuery) {
                mustArray.add(query.toJson());
            } else {
                filterArray.add(query.toJson());
            }
        }
        if (!mustArray.isEmpty()) {
            filterNode.set("must", mustArray);
        }
        filterNode.set("filter", filterArray);
        boolNode.set("bool", filterNode);
        return boolNode;
    }

    public String buildToJson() {
        ObjectNode build = build();
        return build.toString();
    }
}

