package com.immomo.recommend.db.es.vector.type;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/02/20 14:07
 */
public enum MetricType implements Serializable {
    L1(0,"l1"),
    L2(1,"l2_norm"),
    DP(2,"dot_product"),
    COSINE(3,"cosine"),
    BOOL(4,"bool");

    private final int metricType;
    private final String esAlias;

    MetricType(int metricType, String esAlias) {
        this.metricType = metricType;
        this.esAlias = esAlias;
    }

    public int getMetricType() {
        return metricType;
    }

    public String getEsAlias() {
        return esAlias;
    }
}
