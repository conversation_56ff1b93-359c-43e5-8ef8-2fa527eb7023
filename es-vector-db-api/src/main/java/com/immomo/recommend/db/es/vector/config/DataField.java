package com.immomo.recommend.db.es.vector.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.immomo.recommend.db.es.vector.type.FieldType;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/02/20 14:11
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataField implements Serializable {
    private String field;
    private FieldType type;
    private FieldType elementType;

    public DataField() {
    }

    public DataField(String field, FieldType type, FieldType elementType) {
        this.field = field;
        this.type = type;
        this.elementType = elementType;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public FieldType getType() {
        return type;
    }

    public void setType(FieldType type) {
        this.type = type;
    }

    public FieldType getElementType() {
        return elementType;
    }

    public void setElementType(FieldType elementType) {
        this.elementType = elementType;
    }
}
