package com.immomo.recommend.db.es.vector.query.builder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * 成都特殊需求
 *
 * <AUTHOR>
 * @date 2025/02/27 15:11
 */
public class MustMatchQuery extends Query {

    private String field;
    private String value;

    public MustMatchQuery(String field, String value) {
        this.field = field;
        this.value = value;
    }

    @Override
    public ObjectNode toJson() {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode matchNode = mapper.createObjectNode();
        matchNode.put(field, value);
        ObjectNode matchWrapperNode = mapper.createObjectNode();
        matchWrapperNode.set("match", matchNode);
        return matchWrapperNode;
    }
}
