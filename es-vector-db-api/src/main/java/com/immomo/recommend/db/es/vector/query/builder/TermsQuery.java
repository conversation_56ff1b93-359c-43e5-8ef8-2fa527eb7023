package com.immomo.recommend.db.es.vector.query.builder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/02/25 16:43
 */
public class TermsQuery extends Query {
    private String field;
    private List<Object> values;

    public TermsQuery(String field, List<Object> values) {
        this.field = field;
        this.values = values;
    }

    @Override
    public ObjectNode toJson() {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode termsNode = mapper.createObjectNode();
        termsNode.putPOJO(field, values);
        ObjectNode queryNode = mapper.createObjectNode();
        queryNode.set("terms", termsNode);
        return queryNode;
    }
}
