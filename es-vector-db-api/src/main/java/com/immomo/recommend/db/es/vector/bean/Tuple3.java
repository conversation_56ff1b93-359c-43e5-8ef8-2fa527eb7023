package com.immomo.recommend.db.es.vector.bean;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/02/20 11:40
 */
public class Tuple3<F1,F2,F3> implements Serializable {
    private F1 f1;
    private F2 f2;
    private F3 f3;

    public Tuple3() {
    }

    public Tuple3(F1 f1, F2 f2, F3 f3) {
        this.f1 = f1;
        this.f2 = f2;
        this.f3 = f3;
    }

    public F1 getF1() {
        return f1;
    }

    public void setF1(F1 f1) {
        this.f1 = f1;
    }

    public F2 getF2() {
        return f2;
    }

    public void setF2(F2 f2) {
        this.f2 = f2;
    }

    public F3 getF3() {
        return f3;
    }

    public void setF3(F3 f3) {
        this.f3 = f3;
    }
}
