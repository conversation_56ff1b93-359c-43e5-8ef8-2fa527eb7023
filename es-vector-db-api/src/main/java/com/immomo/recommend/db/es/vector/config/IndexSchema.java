package com.immomo.recommend.db.es.vector.config;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/02/20 11:38
 */
public class IndexSchema implements Serializable {
    private List<DataField> schema;
    private List<String> routing;

    public IndexSchema() {
    }

    public IndexSchema(List<DataField> schema, List<String> routing) {
        this.schema = schema;
        this.routing = routing;
    }

    public List<DataField> getSchema() {
        return schema;
    }

    public void setSchema(List<DataField> schema) {
        this.schema = schema;
    }

    public List<String> getRouting() {
        return routing;
    }

    public void setRouting(List<String> routing) {
        this.routing = routing;
    }
}
