package com.immomo.recommend.db.es.vector.type;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/02/20 11:35
 */
public enum ResponseStatus implements Serializable {
    OK(1),
    ERROR(2),
    EXCEPTION(3);
    private final int statusCode;

    ResponseStatus(int statusCode) {
        this.statusCode = statusCode;
    }

    public int getStatusCode() {
        return statusCode;
    }
}
