package com.immomo.recommend.db.es.vector.bean;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/02/27 18:09
 */
public class SearchResponseWithProfile extends Response implements Serializable {
    private List<SimilarItemWithData> items;


    public SearchResponseWithProfile() {
    }

    public SearchResponseWithProfile(List<SimilarItemWithData> items) {
        this.items = items;
    }

    public List<SimilarItemWithData> getItems() {
        return items;
    }

    public void setItems(List<SimilarItemWithData> items) {
        this.items = items;
    }
}
