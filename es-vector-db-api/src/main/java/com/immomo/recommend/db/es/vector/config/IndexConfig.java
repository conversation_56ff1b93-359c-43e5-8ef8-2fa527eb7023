package com.immomo.recommend.db.es.vector.config;

import com.immomo.recommend.db.es.vector.type.IndexType;
import com.immomo.recommend.db.es.vector.type.MetricType;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/02/20 11:38
 */
public class IndexConfig implements Serializable {
    private String indexName;
    private IndexType indexType;
    private MetricType metricType;
    private int shard;
    private int dimension;
    //索引过期时间(秒数)
    private long expireTimeSeconds = -1;

    public IndexConfig() {
    }

    public IndexConfig(String indexName, IndexType indexType, MetricType metricType, int shard, int dimension, long expireTimeSeconds) {
        this.indexName = indexName;
        this.indexType = indexType;
        this.metricType = metricType;
        this.shard = shard;
        this.dimension = dimension;
        this.expireTimeSeconds = expireTimeSeconds;
    }

    public String getIndexName() {
        return indexName;
    }

    public void setIndexName(String indexName) {
        this.indexName = indexName;
    }

    public IndexType getIndexType() {
        return indexType;
    }

    public void setIndexType(IndexType indexType) {
        this.indexType = indexType;
    }

    public MetricType getMetricType() {
        return metricType;
    }

    public void setMetricType(MetricType metricType) {
        this.metricType = metricType;
    }

    public int getShard() {
        return shard;
    }

    public void setShard(int shard) {
        this.shard = shard;
    }

    public int getDimension() {
        return dimension;
    }

    public void setDimension(int dimension) {
        this.dimension = dimension;
    }

    public long getExpireTimeSeconds() {
        return expireTimeSeconds;
    }

    public void setExpireTimeSeconds(long expireTimeSeconds) {
        this.expireTimeSeconds = expireTimeSeconds;
    }
}
