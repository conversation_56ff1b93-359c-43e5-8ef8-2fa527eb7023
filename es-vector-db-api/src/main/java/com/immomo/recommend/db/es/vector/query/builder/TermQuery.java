package com.immomo.recommend.db.es.vector.query.builder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * <AUTHOR>
 * @date 2025/02/25 16:43
 */
public class TermQuery extends Query {
    private String field;
    private Object value;

    public TermQuery(String field, Object value) {
        this.field = field;
        this.value = value;
    }

    @Override
    public ObjectNode toJson() {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode termNode = mapper.createObjectNode();
        termNode.put(field, value.toString());
        ObjectNode queryNode = mapper.createObjectNode();
        queryNode.set("term", termNode);
        return queryNode;
    }
}
