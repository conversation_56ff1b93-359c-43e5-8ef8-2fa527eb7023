package com.immomo.recommend.db.es.vector.bean;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/02/24 15:55
 */
public class SimilarItemWithData extends SimilarItem implements Similar, Serializable {
    private Map<String, Object> data;

    public SimilarItemWithData() {
    }

    public SimilarItemWithData(Map<String, Object> data) {
        this.data = data;
    }

    public SimilarItemWithData(String id, float score, Map<String, Object> data) {
        super(id, score);
        this.data = data;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }
}
