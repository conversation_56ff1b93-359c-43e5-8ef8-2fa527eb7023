package com.immomo.recommend.db.es.vector.query.builder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * <AUTHOR>
 * @date 2025/02/25 16:43
 */
public class DistanceQuery extends Query {
    private String field;
    private double distance;
    private String unit;
    private double lat;
    private double lon;

    public DistanceQuery(String field, double distance, String unit, double lat, double lon) {
        this.field = field;
        this.distance = distance;
        this.unit = unit;
        this.lat = lat;
        this.lon = lon;
    }

    @Override
    public ObjectNode toJson() {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode distanceNode = mapper.createObjectNode();
        distanceNode.put("distance", distance + unit);
        ObjectNode locationNode = mapper.createObjectNode();
        locationNode.put("lat", lat);
        locationNode.put("lon", lon);
        distanceNode.set("location", locationNode);
        ObjectNode queryNode = mapper.createObjectNode();
        queryNode.set("geo_distance", distanceNode);
        return queryNode;
    }
}
