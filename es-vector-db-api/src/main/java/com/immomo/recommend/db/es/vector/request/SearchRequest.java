package com.immomo.recommend.db.es.vector.request;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/03/05 19:27
 */
public class SearchRequest implements Serializable {
    private String indexName;
    private float[] searchVector;
    private int limit;
    private Float scoreGt;
    private Float scoreLt;
    private String queryDsl;

    public SearchRequest() {
    }

    public SearchRequest(String indexName, float[] searchVector, int limit, Float scoreGt, Float scoreLt, String queryDsl) {
        this.indexName = indexName;
        this.searchVector = searchVector;
        this.limit = limit;
        this.scoreGt = scoreGt;
        this.scoreLt = scoreLt;
        this.queryDsl = queryDsl;
    }

    public String getIndexName() {
        return indexName;
    }

    public void setIndexName(String indexName) {
        this.indexName = indexName;
    }

    public float[] getSearchVector() {
        return searchVector;
    }

    public void setSearchVector(float[] searchVector) {
        this.searchVector = searchVector;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public Float getScoreGt() {
        return scoreGt;
    }

    public void setScoreGt(Float scoreGt) {
        this.scoreGt = scoreGt;
    }

    public Float getScoreLt() {
        return scoreLt;
    }

    public void setScoreLt(Float scoreLt) {
        this.scoreLt = scoreLt;
    }

    public String getQueryDsl() {
        return queryDsl;
    }

    public void setQueryDsl(String queryDsl) {
        this.queryDsl = queryDsl;
    }
}
