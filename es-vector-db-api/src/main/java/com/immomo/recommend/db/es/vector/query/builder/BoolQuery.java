package com.immomo.recommend.db.es.vector.query.builder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * <AUTHOR>
 * @date 2025/02/25 16:42
 */
public class BoolQuery extends Query {
    @Override
    public ObjectNode toJson() {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode boolNode = mapper.createObjectNode();
        ObjectNode queryNode = mapper.createObjectNode();
        queryNode.set("bool", boolNode);
        return queryNode;
    }
}
