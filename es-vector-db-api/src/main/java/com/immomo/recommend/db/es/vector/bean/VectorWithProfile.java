package com.immomo.recommend.db.es.vector.bean;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/02/24 15:57
 */
public class VectorWithProfile implements Serializable {
    private float[] vectorData;
    private Map<String, Object> data;

    public VectorWithProfile() {
    }

    public VectorWithProfile(float[] vectorData, Map<String, Object> data) {
        this.vectorData = vectorData;
        this.data = data;
    }

    public float[] getVectorData() {
        return vectorData;
    }

    public void setVectorData(float[] vectorData) {
        this.vectorData = vectorData;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }
}
