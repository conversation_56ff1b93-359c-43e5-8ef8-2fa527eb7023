package com.immomo.recommend.db.es.vector;

import com.immomo.recommend.db.es.vector.bean.BulkResponse;
import com.immomo.recommend.db.es.vector.bean.CreateResponse;
import com.immomo.recommend.db.es.vector.bean.SearchResponse;
import com.immomo.recommend.db.es.vector.bean.Tuple3;
import com.immomo.recommend.db.es.vector.bean.UpdateResponse;
import com.immomo.recommend.db.es.vector.bean.VectorWithProfile;
import com.immomo.recommend.db.es.vector.config.IndexConfig;
import com.immomo.recommend.db.es.vector.config.IndexSchema;
import com.immomo.recommend.db.es.vector.request.SearchRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/02/20 11:30
 */
public interface VectorApi {

    void test();

    //创建索引,调用后端接口，记录索引及schema信息保存到数据库
    CreateResponse createIndex(IndexConfig indexConfig, IndexSchema indexSchema);

    //写入数据
    /**
     profile.put("gender","M");
     profile.put("cityCode","1101");
     profile.put("location",new GeoPoint(39.99,116,39))
     profile.put("age",25);
     insert("test_vec_index01","ak97979",[0.888,0.123,0.555,0.234,0.923,0.153,0.123,0.123],data);
     **/
    /**
     * @Param indexName 索引名称
     * @Param id 数据id
     * @Param vectorData 向量数据
     * @Param profile 正排数据(标量)
     * @Param routing 路由分片策略，如使用性别+城市则指定为“M_1101”
     **/
    UpdateResponse insert(String indexName, String id, float[] vectorData, Map<String, Object> profile);

        // {
    //   total: number,
    //   failed: number,
    //   successful: number,
    //   time: number,
    //   aborted: boolean
    // }
    BulkResponse bulkInsert(String indexName, List<Tuple3<String, float[], Map<String, Object>>> datas);

    //更新接口
    UpdateResponse update(String indexName, String id, float[] vectorData, Map<String, Object> profile);

    UpdateResponse updateVector(String indexName, String id, float[] vectorData);

    UpdateResponse updateProfile(String indexName, String id, Map<String, Object> profile);

    //删除
    UpdateResponse delete(String indexName, String id);

    VectorWithProfile searchById(String indexName,String id);

    //查询相似数据
    /**
     * @Param indexName 索引名称
     * @Param searchVector 检索的向量
     * @Param metricType 距离计算方式
     * @Param limit 返回数据top
     * @Param scoreFilter 距离过滤值，大于这个值的数据才会返回，null则不过滤
     **/

    SearchResponse search(SearchRequest searchRequest);

    void cleanExpiredData(String indexName);
}
