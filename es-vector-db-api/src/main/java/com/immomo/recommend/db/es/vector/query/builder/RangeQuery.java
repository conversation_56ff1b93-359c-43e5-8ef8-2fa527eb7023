package com.immomo.recommend.db.es.vector.query.builder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * <AUTHOR>
 * @date 2025/02/25 16:43
 */
public class RangeQuery extends Query {
    private String field;
    private Object from;
    private Object to;

    public RangeQuery(String field, Object from, Object to) {
        this.field = field;
        this.from = from;
        this.to = to;
    }

    @Override
    public ObjectNode toJson() {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode rangeNode = mapper.createObjectNode();
        rangeNode.put("gte", from.toString());
        rangeNode.put("lte", to.toString());
        ObjectNode fieldNode = mapper.createObjectNode();
        fieldNode.set(field, rangeNode);
        ObjectNode queryNode = mapper.createObjectNode();
        queryNode.set("range", fieldNode);
        return queryNode;
    }
}
