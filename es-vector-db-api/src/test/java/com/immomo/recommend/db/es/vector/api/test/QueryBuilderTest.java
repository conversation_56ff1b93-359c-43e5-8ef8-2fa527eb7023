package com.immomo.recommend.db.es.vector.api.test;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.immomo.recommend.db.es.vector.query.builder.DistanceQuery;
import com.immomo.recommend.db.es.vector.query.builder.QueryBuilder;
import com.immomo.recommend.db.es.vector.query.builder.RangeQuery;
import com.immomo.recommend.db.es.vector.query.builder.TermQuery;
import com.immomo.recommend.db.es.vector.query.builder.TermsQuery;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/02/25 16:45
 */
public class QueryBuilderTest {

    @Before
    public void before() {
        QueryBuilder boolBuilder = new QueryBuilder();
        boolBuilder.add(new TermQuery("age", 18));
        boolBuilder.add(new TermQuery("gender", 1));
        boolBuilder.add(new RangeQuery("age", 19, 35));
        List<Object> codeLanguages = new ArrayList<>();
        codeLanguages.add(1.5f);
        codeLanguages.add(2f);
        codeLanguages.add(3f);
        boolBuilder.add(new TermsQuery("codeLanguage", codeLanguages));
        boolBuilder.add(new DistanceQuery("location", 30, "km", 39.99, 116.39));
    }

    @Test
    public void test() {
        QueryBuilder boolBuilder = new QueryBuilder();
        boolBuilder.add(new TermQuery("age", 18));
        boolBuilder.add(new TermQuery("gender", 1));
        boolBuilder.add(new RangeQuery("age", 19, 35));
        List<Object> codeLanguages = new ArrayList<>();
        codeLanguages.add("go");
        codeLanguages.add("c++");
        codeLanguages.add("python");
        boolBuilder.add(new TermsQuery("codeLanguage", codeLanguages));
        boolBuilder.add(new DistanceQuery("location", 30, "km", 39.99, 116.39));
        ObjectNode boolQueryJson = boolBuilder.build();
        System.out.println(boolQueryJson.toPrettyString());
    }

    @Test
    public void test2() throws Exception {
        QueryBuilder boolBuilder = new QueryBuilder();
        boolBuilder.add(new TermQuery("age", 18));
        boolBuilder.add(new TermQuery("gender", 1));
        boolBuilder.add(new RangeQuery("age", 19, 35));
        List<Object> codeLanguages = new ArrayList<>();
        codeLanguages.add("go");
        codeLanguages.add("c++");
        codeLanguages.add("python");
        boolBuilder.add(new TermsQuery("codeLanguage", codeLanguages));
        boolBuilder.add(new DistanceQuery("location", 30, "km", 39.99, 116.39));
        ObjectMapper objectMapper = new ObjectMapper();
        String serialized = objectMapper.writer().writeValueAsString(boolBuilder.build());

        ObjectNode jsonNodes = objectMapper.reader().readValue(serialized, ObjectNode.class);
        System.out.println(serialized);
        System.out.println(jsonNodes);
    }
}
